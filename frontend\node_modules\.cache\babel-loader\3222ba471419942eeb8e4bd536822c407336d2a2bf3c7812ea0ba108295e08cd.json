{"ast": null, "code": "import { ref, onMounted } from \"vue\";\nexport default {\n  name: \"OperationRecords\",\n  setup() {\n    const records = ref([]);\n    const loading = ref(true);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n    const fetchRecords = async () => {\n      try {\n        loading.value = true;\n        // 这里应该调用用户操作记录API\n        // 暂时使用空数组，等待后端API实现\n        records.value = [];\n        total.value = 0;\n      } catch (error) {\n        console.error(\"获取操作记录失败:\", error);\n        records.value = [];\n        total.value = 0;\n      } finally {\n        loading.value = false;\n      }\n    };\n    const handleSizeChange = val => {\n      pageSize.value = val;\n      fetchRecords();\n    };\n    const handleCurrentChange = val => {\n      currentPage.value = val;\n      fetchRecords();\n    };\n    onMounted(() => {\n      fetchRecords();\n    });\n    return {\n      records,\n      loading,\n      currentPage,\n      pageSize,\n      total,\n      handleSizeChange,\n      handleCurrentChange\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "name", "setup", "records", "loading", "currentPage", "pageSize", "total", "fetchRecords", "value", "error", "console", "handleSizeChange", "val", "handleCurrentChange"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\user\\OperationRecords.vue"], "sourcesContent": ["<template>\n  <div class=\"operation-records-container\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h2>操作记录</h2>\n        </div>\n      </template>\n\n      <el-table :data=\"records\" style=\"width: 100%\" v-loading=\"loading\">\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" />\n        <el-table-column prop=\"operation_type\" label=\"操作类型\" width=\"120\" />\n        <el-table-column prop=\"operation_time\" label=\"操作时间\" width=\"180\" />\n        <el-table-column prop=\"operation_content\" label=\"操作内容\" />\n        <el-table-column prop=\"status\" label=\"状态\" width=\"100\">\n          <template #default=\"scope\">\n            <el-tag :type=\"scope.row.status === '成功' ? 'success' : 'danger'\">\n              {{ scope.row.status }}\n            </el-tag>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <div class=\"pagination-container\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :page-sizes=\"[10, 20, 50, 100]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n        />\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted } from \"vue\";\n\nexport default {\n  name: \"OperationRecords\",\n  setup() {\n    const records = ref([]);\n    const loading = ref(true);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n\n    const fetchRecords = async () => {\n      try {\n        loading.value = true;\n        // 这里应该调用用户操作记录API\n        // 暂时使用空数组，等待后端API实现\n        records.value = [];\n        total.value = 0;\n      } catch (error) {\n        console.error(\"获取操作记录失败:\", error);\n        records.value = [];\n        total.value = 0;\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    const handleSizeChange = (val) => {\n      pageSize.value = val;\n      fetchRecords();\n    };\n\n    const handleCurrentChange = (val) => {\n      currentPage.value = val;\n      fetchRecords();\n    };\n\n    onMounted(() => {\n      fetchRecords();\n    });\n\n    return {\n      records,\n      loading,\n      currentPage,\n      pageSize,\n      total,\n      handleSizeChange,\n      handleCurrentChange,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.operation-records-container {\n  padding: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  display: flex;\n  justify-content: flex-end;\n}\n</style>\n"], "mappings": "AAuCA,SAASA,GAAG,EAAEC,SAAQ,QAAS,KAAK;AAEpC,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,KAAKA,CAAA,EAAG;IACN,MAAMC,OAAM,GAAIJ,GAAG,CAAC,EAAE,CAAC;IACvB,MAAMK,OAAM,GAAIL,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMM,WAAU,GAAIN,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMO,QAAO,GAAIP,GAAG,CAAC,EAAE,CAAC;IACxB,MAAMQ,KAAI,GAAIR,GAAG,CAAC,CAAC,CAAC;IAEpB,MAAMS,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFJ,OAAO,CAACK,KAAI,GAAI,IAAI;QACpB;QACA;QACAN,OAAO,CAACM,KAAI,GAAI,EAAE;QAClBF,KAAK,CAACE,KAAI,GAAI,CAAC;MACjB,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCP,OAAO,CAACM,KAAI,GAAI,EAAE;QAClBF,KAAK,CAACE,KAAI,GAAI,CAAC;MACjB,UAAU;QACRL,OAAO,CAACK,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;IAED,MAAMG,gBAAe,GAAKC,GAAG,IAAK;MAChCP,QAAQ,CAACG,KAAI,GAAII,GAAG;MACpBL,YAAY,CAAC,CAAC;IAChB,CAAC;IAED,MAAMM,mBAAkB,GAAKD,GAAG,IAAK;MACnCR,WAAW,CAACI,KAAI,GAAII,GAAG;MACvBL,YAAY,CAAC,CAAC;IAChB,CAAC;IAEDR,SAAS,CAAC,MAAM;MACdQ,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,OAAO;MACLL,OAAO;MACPC,OAAO;MACPC,WAAW;MACXC,QAAQ;MACRC,KAAK;MACLK,gBAAgB;MAChBE;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}