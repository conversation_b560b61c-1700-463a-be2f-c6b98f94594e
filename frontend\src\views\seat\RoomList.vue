<template>
  <div class="room-list">
    <div class="page-header">
      <h2>自习室列表</h2>
      <div class="header-actions">
        <el-input
          v-model="searchQuery"
          placeholder="搜索自习室"
          :prefix-icon="Search"
          clearable
          @clear="handleSearch"
          @input="handleSearch"
          class="search-input"
        />
        <el-button type="primary" @click="refreshRooms">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <div class="filter-section">
      <el-card shadow="never">
        <div class="filter-container">
          <div class="filter-item">
            <span class="filter-label">楼层：</span>
            <el-select
              v-model="filters.floor"
              placeholder="全部楼层"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="floor in floorOptions"
                :key="floor.value"
                :label="floor.label"
                :value="floor.value"
              />
            </el-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">状态：</span>
            <el-select
              v-model="filters.status"
              placeholder="全部状态"
              clearable
              @change="handleFilterChange"
            >
              <el-option
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">排序：</span>
            <el-select
              v-model="sortBy"
              placeholder="排序方式"
              @change="handleSortChange"
            >
              <el-option
                v-for="option in sortOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </div>
      </el-card>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <el-skeleton :rows="3" animated style="margin-top: 20px" />
    </div>

    <div v-else-if="filteredRooms.length === 0" class="empty-container">
      <el-empty description="没有找到符合条件的自习室" />
    </div>

    <div v-else class="room-grid">
      <el-card
        v-for="room in filteredRooms"
        :key="room.id"
        class="room-card"
        :class="{ 'room-closed': room.status !== 'open' }"
        @click="viewRoomDetail(room)"
      >
        <div class="room-header">
          <h3>{{ room.name }}</h3>
          <el-tag :type="getStatusType(room.status)">
            {{ getStatusText(room.status) }}
          </el-tag>
        </div>

        <div class="room-info">
          <div class="info-item">
            <el-icon><Location /></el-icon>
            <span>{{ room.location }}</span>
          </div>

          <div class="info-item">
            <el-icon><OfficeBuilding /></el-icon>
            <span>{{ room.floor }}楼</span>
          </div>

          <div class="info-item">
            <el-icon><User /></el-icon>
            <span>容量: {{ room.capacity }}座</span>
          </div>

          <div class="info-item">
            <el-icon><Clock /></el-icon>
            <span>
              开放时间: {{ formatTime(room.open_time) }} -
              {{ formatTime(room.close_time) }}
            </span>
          </div>
        </div>

        <div class="room-footer">
          <div class="seat-availability">
            <div class="progress-label">
              <span>可用座位</span>
              <span>{{ room.available_seats }}/{{ room.capacity }}</span>
            </div>
            <el-progress
              :percentage="(room.available_seats / room.capacity) * 100"
              :format="formatAvailability"
              :stroke-width="10"
              :color="getAvailabilityColor(room.available_seats, room.capacity)"
            />
          </div>

          <el-button
            type="primary"
            size="small"
            :disabled="room.status !== 'open'"
          >
            查看座位
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, reactive } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  Search,
  Refresh,
  Location,
  OfficeBuilding,
  User,
  Clock,
} from "@element-plus/icons-vue";
// 导入API
import seatApi from "@/api/seat";

export default {
  name: "RoomList",
  components: {
    Refresh,
    Location,
    OfficeBuilding,
    User,
    Clock,
  },
  setup() {
    const router = useRouter();

    const loading = ref(true);
    const searchQuery = ref("");
    const sortBy = ref("floor");
    const roomsData = ref([]);

    const filters = reactive({
      floor: "",
      status: "",
    });

    // 楼层选项 - 根据模拟数据动态生成
    const floorOptions = computed(() => {
      const floors = [...new Set(mockRooms.map((room) => room.floor))].sort();
      return floors.map((floor) => ({ value: floor, label: `${floor}楼` }));
    });

    // 状态选项
    const statusOptions = [
      { value: "open", label: "开放中" },
      { value: "closed", label: "已关闭" },
      { value: "maintenance", label: "维护中" },
    ];

    // 排序选项
    const sortOptions = [
      { value: "floor", label: "按楼层排序" },
      { value: "name", label: "按名称排序" },
      { value: "capacity", label: "按容量排序" },
      { value: "available", label: "按可用座位排序" },
    ];

    // 获取自习室列表
    const getRooms = async () => {
      try {
        loading.value = true;
        // 调用真实API
        const response = await seatApi.getRooms();
        roomsData.value = response.data.results || response.data;
      } catch (error) {
        console.error("获取自习室列表失败:", error);
        ElMessage.error("获取自习室列表失败");
      } finally {
        loading.value = false;
      }
    };

    // 刷新自习室列表
    const refreshRooms = () => {
      getRooms();
    };

    // 过滤后的自习室列表
    const filteredRooms = computed(() => {
      let result = roomsData.value;

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(
          (room) =>
            room.name.toLowerCase().includes(query) ||
            room.location.toLowerCase().includes(query)
        );
      }

      // 楼层过滤
      if (filters.floor) {
        result = result.filter((room) => room.floor === filters.floor);
      }

      // 状态过滤
      if (filters.status) {
        result = result.filter((room) => room.status === filters.status);
      }

      // 排序
      result = [...result].sort((a, b) => {
        switch (sortBy.value) {
          case "name":
            return a.name.localeCompare(b.name);
          case "capacity":
            return b.capacity - a.capacity;
          case "available":
            return (
              b.available_seats / b.capacity - a.available_seats / a.capacity
            );
          case "floor":
          default:
            return a.floor - b.floor || a.name.localeCompare(b.name);
        }
      });

      return result;
    });

    // 处理搜索
    const handleSearch = () => {
      // 搜索逻辑已在计算属性中实现
    };

    // 处理过滤变化
    const handleFilterChange = () => {
      // 过滤逻辑已在计算属性中实现
    };

    // 处理排序变化
    const handleSortChange = () => {
      // 排序逻辑已在计算属性中实现
    };

    // 查看自习室详情
    const viewRoomDetail = (room) => {
      if (room.status !== "open") {
        ElMessage.warning("该自习室当前不开放");
        return;
      }

      router.push(`/seat/map?roomId=${room.id}`);
    };

    // 格式化时间
    const formatTime = (timeString) => {
      if (!timeString) return "";

      // 时间格式为 "HH:MM:SS"，只显示 "HH:MM"
      return timeString.substring(0, 5);
    };

    // 获取状态类型
    const getStatusType = (status) => {
      switch (status) {
        case "open":
          return "success";
        case "closed":
          return "danger";
        case "maintenance":
          return "warning";
        default:
          return "info";
      }
    };

    // 获取状态文本
    const getStatusText = (status) => {
      switch (status) {
        case "open":
          return "开放中";
        case "closed":
          return "已关闭";
        case "maintenance":
          return "维护中";
        default:
          return "未知状态";
      }
    };

    // 格式化可用座位百分比
    const formatAvailability = () => {
      return "";
    };

    // 获取可用座位颜色
    const getAvailabilityColor = (available, total) => {
      const percentage = (available / total) * 100;
      if (percentage <= 20) return "#f56c6c";
      if (percentage <= 50) return "#e6a23c";
      return "#67c23a";
    };

    onMounted(() => {
      getRooms();
    });

    return {
      loading,
      searchQuery,
      filters,
      sortBy,
      floorOptions,
      statusOptions,
      sortOptions,
      filteredRooms,
      refreshRooms,
      handleSearch,
      handleFilterChange,
      handleSortChange,
      viewRoomDetail,
      formatTime,
      getStatusType,
      getStatusText,
      formatAvailability,
      getAvailabilityColor,
      Search,
    };
  },
};
</script>

<style lang="scss" scoped>
.room-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .header-actions {
    display: flex;
    gap: 10px;

    .search-input {
      width: 250px;
    }
  }
}

.filter-section {
  margin-bottom: 20px;

  .filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .filter-item {
      display: flex;
      align-items: center;

      .filter-label {
        margin-right: 10px;
        white-space: nowrap;
      }
    }
  }
}

.loading-container,
.empty-container {
  padding: 40px 0;
  text-align: center;
}

.room-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.room-card {
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  &.room-closed {
    opacity: 0.7;

    &:hover {
      transform: none;
    }
  }

  .room-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h3 {
      margin: 0;
      font-size: 18px;
    }
  }

  .room-info {
    margin-bottom: 15px;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .el-icon {
        margin-right: 8px;
        color: #909399;
      }
    }
  }

  .room-footer {
    .seat-availability {
      margin-bottom: 15px;

      .progress-label {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
        font-size: 14px;
        color: #606266;
      }
    }
  }
}
</style>
