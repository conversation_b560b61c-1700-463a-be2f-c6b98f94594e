{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from \"axios\";\nimport { ElMessage } from \"element-plus\";\nimport router from \"@/router\";\nimport { SM4Crypto } from \"@/utils/crypto\";\n\n// 创建axios实例\nconst http = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || \"/api\",\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\"\n  }\n});\n\n// 请求拦截器\nhttp.interceptors.request.use(async config => {\n  // 从localStorage获取token\n  const token = localStorage.getItem(\"token\");\n\n  // 如果有token，则添加到请求头\n  if (token) {\n    config.headers[\"Authorization\"] = `Bearer ${token}`;\n  }\n\n  // 检查是否需要加密请求数据\n  if (config.encrypt && config.data) {\n    try {\n      // 动态导入加密工具\n      const {\n        default: CryptoUtils\n      } = await import(\"@/utils/cryptoUtils\");\n\n      // 加密请求数据\n      const encryptedPackage = await CryptoUtils.encryptRequest(config.data);\n\n      // 替换请求数据\n      config.data = encryptedPackage;\n\n      // 添加加密标记\n      config.headers[\"X-Encrypted\"] = \"true\";\n    } catch (error) {\n      console.error(\"请求数据加密失败:\", error);\n      // 如果加密失败，继续发送原始数据\n    }\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nhttp.interceptors.response.use(async response => {\n  // 检查响应是否加密\n  if (response.headers[\"x-encrypted\"] === \"true\" && response.data) {\n    try {\n      // 动态导入加密工具\n      const {\n        default: CryptoUtils\n      } = await import(\"@/utils/cryptoUtils\");\n\n      // 获取客户端私钥\n      const privateKey = localStorage.getItem(\"sm2_private_key\");\n      if (!privateKey) {\n        console.warn(\"未找到客户端私钥，无法解密响应\");\n      } else {\n        // 解密响应数据\n        const decryptedData = CryptoUtils.decryptResponse(response.data, privateKey);\n        response.data = decryptedData;\n      }\n    } catch (error) {\n      console.error(\"响应解密失败:\", error);\n      // 如果解密失败，继续使用原始数据\n    }\n  }\n\n  // 检查响应状态\n  if (response.data && response.data.code !== undefined) {\n    if (response.data.code === 0) {\n      // 成功响应，返回数据部分\n      return response.data.data || response.data;\n    } else {\n      // 业务错误\n      const error = new Error(response.data.message || \"请求失败\");\n      error.code = response.data.code;\n      return Promise.reject(error);\n    }\n  }\n\n  // 直接返回响应数据\n  return response.data;\n}, error => {\n  if (error.response) {\n    // 处理响应错误\n    switch (error.response.status) {\n      case 401:\n        // 未授权，清除token并跳转到登录页\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"userInfo\");\n\n        // 如果不是登录页，则跳转到登录页\n        if (router.currentRoute.value.path !== \"/login\") {\n          ElMessage.error(\"登录已过期，请重新登录\");\n          router.push(\"/login\");\n        }\n        break;\n      case 403:\n        // 禁止访问\n        ElMessage.error(\"没有权限访问该资源\");\n        break;\n      case 404:\n        // 资源不存在\n        ElMessage.error(\"请求的资源不存在\");\n        break;\n      case 500:\n        // 服务器错误\n        ElMessage.error(\"服务器错误，请稍后重试\");\n        break;\n      default:\n        // 其他错误\n        if (error.response.data && error.response.data.message) {\n          ElMessage.error(error.response.data.message);\n        } else {\n          ElMessage.error(\"请求失败，请稍后重试\");\n        }\n    }\n  } else if (error.request) {\n    // 请求发送但没有收到响应\n    ElMessage.error(\"网络错误，请检查网络连接\");\n  } else {\n    // 请求配置错误\n    ElMessage.error(\"请求配置错误\");\n  }\n  return Promise.reject(error);\n});\n\n// 导出请求方法\nexport default {\n  // GET请求\n  get(url, params = {}, config = {}) {\n    return http.get(url, {\n      params,\n      ...config\n    });\n  },\n  // POST请求\n  post(url, data = {}, config = {}) {\n    return http.post(url, data, config);\n  },\n  // PUT请求\n  put(url, data = {}, config = {}) {\n    return http.put(url, data, config);\n  },\n  // DELETE请求\n  delete(url, config = {}) {\n    return http.delete(url, config);\n  },\n  // 加密POST请求\n  encryptedPost(url, data = {}, config = {}) {\n    return http.post(url, data, {\n      ...config,\n      encrypt: true\n    });\n  },\n  // 加密PUT请求\n  encryptedPut(url, data = {}, config = {}) {\n    return http.put(url, data, {\n      ...config,\n      encrypt: true\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "ElMessage", "router", "SM4Crypto", "http", "create", "baseURL", "process", "env", "VUE_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "encrypt", "data", "default", "CryptoUtils", "encryptedPackage", "encryptRequest", "error", "console", "Promise", "reject", "response", "privateKey", "warn", "decryptedData", "decryptResponse", "code", "undefined", "Error", "message", "status", "removeItem", "currentRoute", "value", "path", "push", "get", "url", "params", "post", "put", "delete", "encryptedPost", "encryptedPut"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/api/http.js"], "sourcesContent": ["import axios from \"axios\";\nimport { ElMessage } from \"element-plus\";\nimport router from \"@/router\";\nimport { SM4Crypto } from \"@/utils/crypto\";\n\n// 创建axios实例\nconst http = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || \"/api\",\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 请求拦截器\nhttp.interceptors.request.use(\n  async (config) => {\n    // 从localStorage获取token\n    const token = localStorage.getItem(\"token\");\n\n    // 如果有token，则添加到请求头\n    if (token) {\n      config.headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n\n    // 检查是否需要加密请求数据\n    if (config.encrypt && config.data) {\n      try {\n        // 动态导入加密工具\n        const { default: CryptoUtils } = await import(\"@/utils/cryptoUtils\");\n\n        // 加密请求数据\n        const encryptedPackage = await CryptoUtils.encryptRequest(config.data);\n\n        // 替换请求数据\n        config.data = encryptedPackage;\n\n        // 添加加密标记\n        config.headers[\"X-Encrypted\"] = \"true\";\n      } catch (error) {\n        console.error(\"请求数据加密失败:\", error);\n        // 如果加密失败，继续发送原始数据\n      }\n    }\n\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nhttp.interceptors.response.use(\n  async (response) => {\n    // 检查响应是否加密\n    if (response.headers[\"x-encrypted\"] === \"true\" && response.data) {\n      try {\n        // 动态导入加密工具\n        const { default: CryptoUtils } = await import(\"@/utils/cryptoUtils\");\n\n        // 获取客户端私钥\n        const privateKey = localStorage.getItem(\"sm2_private_key\");\n        if (!privateKey) {\n          console.warn(\"未找到客户端私钥，无法解密响应\");\n        } else {\n          // 解密响应数据\n          const decryptedData = CryptoUtils.decryptResponse(\n            response.data,\n            privateKey\n          );\n          response.data = decryptedData;\n        }\n      } catch (error) {\n        console.error(\"响应解密失败:\", error);\n        // 如果解密失败，继续使用原始数据\n      }\n    }\n\n    // 检查响应状态\n    if (response.data && response.data.code !== undefined) {\n      if (response.data.code === 0) {\n        // 成功响应，返回数据部分\n        return response.data.data || response.data;\n      } else {\n        // 业务错误\n        const error = new Error(response.data.message || \"请求失败\");\n        error.code = response.data.code;\n        return Promise.reject(error);\n      }\n    }\n\n    // 直接返回响应数据\n    return response.data;\n  },\n  (error) => {\n    if (error.response) {\n      // 处理响应错误\n      switch (error.response.status) {\n        case 401:\n          // 未授权，清除token并跳转到登录页\n          localStorage.removeItem(\"token\");\n          localStorage.removeItem(\"userInfo\");\n\n          // 如果不是登录页，则跳转到登录页\n          if (router.currentRoute.value.path !== \"/login\") {\n            ElMessage.error(\"登录已过期，请重新登录\");\n            router.push(\"/login\");\n          }\n          break;\n\n        case 403:\n          // 禁止访问\n          ElMessage.error(\"没有权限访问该资源\");\n          break;\n\n        case 404:\n          // 资源不存在\n          ElMessage.error(\"请求的资源不存在\");\n          break;\n\n        case 500:\n          // 服务器错误\n          ElMessage.error(\"服务器错误，请稍后重试\");\n          break;\n\n        default:\n          // 其他错误\n          if (error.response.data && error.response.data.message) {\n            ElMessage.error(error.response.data.message);\n          } else {\n            ElMessage.error(\"请求失败，请稍后重试\");\n          }\n      }\n    } else if (error.request) {\n      // 请求发送但没有收到响应\n      ElMessage.error(\"网络错误，请检查网络连接\");\n    } else {\n      // 请求配置错误\n      ElMessage.error(\"请求配置错误\");\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// 导出请求方法\nexport default {\n  // GET请求\n  get(url, params = {}, config = {}) {\n    return http.get(url, { params, ...config });\n  },\n\n  // POST请求\n  post(url, data = {}, config = {}) {\n    return http.post(url, data, config);\n  },\n\n  // PUT请求\n  put(url, data = {}, config = {}) {\n    return http.put(url, data, config);\n  },\n\n  // DELETE请求\n  delete(url, config = {}) {\n    return http.delete(url, config);\n  },\n\n  // 加密POST请求\n  encryptedPost(url, data = {}, config = {}) {\n    return http.post(url, data, { ...config, encrypt: true });\n  },\n\n  // 加密PUT请求\n  encryptedPut(url, data = {}, config = {}) {\n    return http.put(url, data, { ...config, encrypt: true });\n  },\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA,MAAMC,IAAI,GAAGJ,KAAK,CAACK,MAAM,CAAC;EACxBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,MAAM;EAC9CC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,IAAI,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CAC3B,MAAOC,MAAM,IAAK;EAChB;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;EAE3C;EACA,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUK,KAAK,EAAE;EACrD;;EAEA;EACA,IAAID,MAAM,CAACI,OAAO,IAAIJ,MAAM,CAACK,IAAI,EAAE;IACjC,IAAI;MACF;MACA,MAAM;QAAEC,OAAO,EAAEC;MAAY,CAAC,GAAG,MAAM,MAAM,CAAC,qBAAqB,CAAC;;MAEpE;MACA,MAAMC,gBAAgB,GAAG,MAAMD,WAAW,CAACE,cAAc,CAACT,MAAM,CAACK,IAAI,CAAC;;MAEtE;MACAL,MAAM,CAACK,IAAI,GAAGG,gBAAgB;;MAE9B;MACAR,MAAM,CAACJ,OAAO,CAAC,aAAa,CAAC,GAAG,MAAM;IACxC,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;IACF;EACF;EAEA,OAAOV,MAAM;AACf,CAAC,EACAU,KAAK,IAAK;EACT,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACArB,IAAI,CAACQ,YAAY,CAACiB,QAAQ,CAACf,GAAG,CAC5B,MAAOe,QAAQ,IAAK;EAClB;EACA,IAAIA,QAAQ,CAAClB,OAAO,CAAC,aAAa,CAAC,KAAK,MAAM,IAAIkB,QAAQ,CAACT,IAAI,EAAE;IAC/D,IAAI;MACF;MACA,MAAM;QAAEC,OAAO,EAAEC;MAAY,CAAC,GAAG,MAAM,MAAM,CAAC,qBAAqB,CAAC;;MAEpE;MACA,MAAMQ,UAAU,GAAGb,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;MAC1D,IAAI,CAACY,UAAU,EAAE;QACfJ,OAAO,CAACK,IAAI,CAAC,iBAAiB,CAAC;MACjC,CAAC,MAAM;QACL;QACA,MAAMC,aAAa,GAAGV,WAAW,CAACW,eAAe,CAC/CJ,QAAQ,CAACT,IAAI,EACbU,UACF,CAAC;QACDD,QAAQ,CAACT,IAAI,GAAGY,aAAa;MAC/B;IACF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B;IACF;EACF;;EAEA;EACA,IAAII,QAAQ,CAACT,IAAI,IAAIS,QAAQ,CAACT,IAAI,CAACc,IAAI,KAAKC,SAAS,EAAE;IACrD,IAAIN,QAAQ,CAACT,IAAI,CAACc,IAAI,KAAK,CAAC,EAAE;MAC5B;MACA,OAAOL,QAAQ,CAACT,IAAI,CAACA,IAAI,IAAIS,QAAQ,CAACT,IAAI;IAC5C,CAAC,MAAM;MACL;MACA,MAAMK,KAAK,GAAG,IAAIW,KAAK,CAACP,QAAQ,CAACT,IAAI,CAACiB,OAAO,IAAI,MAAM,CAAC;MACxDZ,KAAK,CAACS,IAAI,GAAGL,QAAQ,CAACT,IAAI,CAACc,IAAI;MAC/B,OAAOP,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;IAC9B;EACF;;EAEA;EACA,OAAOI,QAAQ,CAACT,IAAI;AACtB,CAAC,EACAK,KAAK,IAAK;EACT,IAAIA,KAAK,CAACI,QAAQ,EAAE;IAClB;IACA,QAAQJ,KAAK,CAACI,QAAQ,CAACS,MAAM;MAC3B,KAAK,GAAG;QACN;QACArB,YAAY,CAACsB,UAAU,CAAC,OAAO,CAAC;QAChCtB,YAAY,CAACsB,UAAU,CAAC,UAAU,CAAC;;QAEnC;QACA,IAAIrC,MAAM,CAACsC,YAAY,CAACC,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC/CzC,SAAS,CAACwB,KAAK,CAAC,aAAa,CAAC;UAC9BvB,MAAM,CAACyC,IAAI,CAAC,QAAQ,CAAC;QACvB;QACA;MAEF,KAAK,GAAG;QACN;QACA1C,SAAS,CAACwB,KAAK,CAAC,WAAW,CAAC;QAC5B;MAEF,KAAK,GAAG;QACN;QACAxB,SAAS,CAACwB,KAAK,CAAC,UAAU,CAAC;QAC3B;MAEF,KAAK,GAAG;QACN;QACAxB,SAAS,CAACwB,KAAK,CAAC,aAAa,CAAC;QAC9B;MAEF;QACE;QACA,IAAIA,KAAK,CAACI,QAAQ,CAACT,IAAI,IAAIK,KAAK,CAACI,QAAQ,CAACT,IAAI,CAACiB,OAAO,EAAE;UACtDpC,SAAS,CAACwB,KAAK,CAACA,KAAK,CAACI,QAAQ,CAACT,IAAI,CAACiB,OAAO,CAAC;QAC9C,CAAC,MAAM;UACLpC,SAAS,CAACwB,KAAK,CAAC,YAAY,CAAC;QAC/B;IACJ;EACF,CAAC,MAAM,IAAIA,KAAK,CAACZ,OAAO,EAAE;IACxB;IACAZ,SAAS,CAACwB,KAAK,CAAC,cAAc,CAAC;EACjC,CAAC,MAAM;IACL;IACAxB,SAAS,CAACwB,KAAK,CAAC,QAAQ,CAAC;EAC3B;EAEA,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,eAAe;EACb;EACAmB,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE/B,MAAM,GAAG,CAAC,CAAC,EAAE;IACjC,OAAOX,IAAI,CAACwC,GAAG,CAACC,GAAG,EAAE;MAAEC,MAAM;MAAE,GAAG/B;IAAO,CAAC,CAAC;EAC7C,CAAC;EAED;EACAgC,IAAIA,CAACF,GAAG,EAAEzB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,EAAE;IAChC,OAAOX,IAAI,CAAC2C,IAAI,CAACF,GAAG,EAAEzB,IAAI,EAAEL,MAAM,CAAC;EACrC,CAAC;EAED;EACAiC,GAAGA,CAACH,GAAG,EAAEzB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,EAAE;IAC/B,OAAOX,IAAI,CAAC4C,GAAG,CAACH,GAAG,EAAEzB,IAAI,EAAEL,MAAM,CAAC;EACpC,CAAC;EAED;EACAkC,MAAMA,CAACJ,GAAG,EAAE9B,MAAM,GAAG,CAAC,CAAC,EAAE;IACvB,OAAOX,IAAI,CAAC6C,MAAM,CAACJ,GAAG,EAAE9B,MAAM,CAAC;EACjC,CAAC;EAED;EACAmC,aAAaA,CAACL,GAAG,EAAEzB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,EAAE;IACzC,OAAOX,IAAI,CAAC2C,IAAI,CAACF,GAAG,EAAEzB,IAAI,EAAE;MAAE,GAAGL,MAAM;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;EAC3D,CAAC;EAED;EACAgC,YAAYA,CAACN,GAAG,EAAEzB,IAAI,GAAG,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,EAAE;IACxC,OAAOX,IAAI,CAAC4C,GAAG,CAACH,GAAG,EAAEzB,IAAI,EAAE;MAAE,GAAGL,MAAM;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;EAC1D;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}