{"ast": null, "code": "/**\n * 混合加密方案工具类\n * 提供前后端混合加密通信功能\n */\nimport { SM2Crypto, SM3Hasher, SM4Crypto } from \"./crypto\";\nimport axios from \"axios\";\n\n/**\n * 加密工具类\n */\nconst CryptoUtils = {\n  /**\n   * 获取服务器SM2公钥\n   * @returns {Promise<string>} 服务器公钥\n   */\n  async getServerPublicKey() {\n    try {\n      // 从API获取服务器公钥，避免硬编码\n      const response = await axios.get(\"/api/v1/crypto/server-public-key\");\n      return response.data.publicKey;\n    } catch (error) {\n      console.error(\"获取服务器公钥失败:\", error);\n      throw new Error(\"获取服务器公钥失败\");\n    }\n  },\n  /**\n   * 加密请求数据\n   * @param {Object|string} data - 原始数据\n   * @returns {Promise<Object>} - 加密后的数据包\n   */\n  async encryptRequest(data) {\n    try {\n      // 获取服务器公钥\n      const serverPublicKey = await this.getServerPublicKey();\n\n      // 生成随机SM4密钥\n      const sm4Key = SM4Crypto.generateKey();\n\n      // 如果是对象，转换为JSON字符串\n      const dataStr = typeof data === \"object\" ? JSON.stringify(data) : data;\n\n      // 使用SM4加密数据\n      const encryptResult = SM4Crypto.encrypt(sm4Key, dataStr);\n\n      // 使用SM2加密SM4密钥\n      const encryptedKey = SM2Crypto.encrypt(serverPublicKey, sm4Key);\n\n      // 返回加密包\n      return {\n        encrypted_data: encryptResult.ciphertext,\n        encrypted_key: encryptedKey,\n        iv: encryptResult.iv\n      };\n    } catch (error) {\n      console.error(\"加密请求数据失败:\", error);\n      throw new Error(\"加密请求数据失败\");\n    }\n  },\n  /**\n   * 解密响应数据\n   * @param {Object} encryptedPackage - 加密包\n   * @param {string} privateKey - 客户端SM2私钥\n   * @returns {Object|string} - 解密后的数据\n   */\n  decryptResponse(encryptedPackage, privateKey) {\n    try {\n      // 提取加密数据\n      const {\n        encrypted_data,\n        encrypted_key,\n        iv\n      } = encryptedPackage;\n      if (!encrypted_data || !encrypted_key || !iv) {\n        throw new Error(\"加密包缺少必要字段\");\n      }\n\n      // 使用SM2解密SM4密钥\n      const sm4Key = SM2Crypto.decrypt(privateKey, encrypted_key);\n\n      // 使用SM4解密数据\n      const decryptedData = SM4Crypto.decrypt(sm4Key, encrypted_data, iv);\n\n      // 尝试解析JSON\n      try {\n        return JSON.parse(decryptedData);\n      } catch (e) {\n        return decryptedData;\n      }\n    } catch (error) {\n      console.error(\"解密响应数据失败:\", error);\n      throw new Error(\"解密响应数据失败\");\n    }\n  },\n  /**\n   * 生成SM2密钥对\n   * @returns {Object} - 包含公钥和私钥的对象\n   */\n  generateSM2KeyPair() {\n    return SM2Crypto.generateKeyPair();\n  },\n  /**\n   * 安全存储SM2私钥\n   * @param {string} privateKey - SM2私钥\n   * @param {string} password - 保护密码\n   */\n  storePrivateKey(privateKey, password) {\n    // 使用密码加密私钥后存储\n    const encryptedPrivateKey = SM4Crypto.encrypt(this.deriveKeyFromPassword(password), privateKey).ciphertext;\n    localStorage.setItem(\"sm2_private_key\", encryptedPrivateKey);\n  },\n  /**\n   * 获取存储的SM2私钥\n   * @param {string} password - 保护密码\n   * @returns {string} - SM2私钥\n   */\n  getStoredPrivateKey(password) {\n    try {\n      const encryptedPrivateKey = localStorage.getItem(\"sm2_private_key\");\n      if (!encryptedPrivateKey) {\n        throw new Error(\"未找到存储的私钥\");\n      }\n\n      // 使用密码解密私钥\n      return SM4Crypto.decrypt(this.deriveKeyFromPassword(password), encryptedPrivateKey);\n    } catch (error) {\n      console.error(\"获取存储的私钥失败:\", error);\n      throw new Error(\"获取存储的私钥失败\");\n    }\n  },\n  /**\n   * 从密码派生密钥\n   * @param {string} password - 用户密码\n   * @returns {string} - 派生的密钥\n   */\n  deriveKeyFromPassword(password) {\n    // 简化实现，实际应使用PBKDF2等密钥派生函数\n    return SM3Hasher.hash(password).substring(0, 32);\n  },\n  /**\n   * 使用SM3哈希算法和盐值对密码进行哈希处理\n   * @param {string} password - 密码\n   * @param {string} salt - 盐值，如果为null则随机生成\n   * @param {number} iterations - 迭代次数\n   * @returns {Object} - 包含哈希值、盐值和迭代次数的对象\n   */\n  hashPassword(password, salt = null, iterations = 10000) {\n    return SM3Hasher.hashWithSalt(password, salt, iterations);\n  },\n  /**\n   * 验证密码是否匹配哈希值\n   * @param {string} password - 密码\n   * @param {string} hashValue - 哈希值(十六进制字符串)\n   * @param {string} salt - 盐值(十六进制字符串)\n   * @param {number} iterations - 迭代次数\n   * @returns {boolean} - 是否匹配\n   */\n  verifyPassword(password, hashValue, salt, iterations = 10000) {\n    return SM3Hasher.verify(password, hashValue, salt, iterations);\n  },\n  /**\n   * 使用SM2签名数据\n   * @param {Object|string} data - 待签名数据\n   * @param {string} privateKey - SM2私钥(十六进制字符串)\n   * @returns {string} - 签名(十六进制字符串)\n   */\n  signData(data, privateKey) {\n    // 如果是对象，转换为JSON字符串\n    const dataStr = typeof data === \"object\" ? JSON.stringify(data) : data;\n    return SM2Crypto.sign(privateKey, dataStr);\n  },\n  /**\n   * 验证SM2签名\n   * @param {Object|string} data - 原始数据\n   * @param {string} signature - 签名(十六进制字符串)\n   * @param {string} publicKey - SM2公钥(十六进制字符串)\n   * @returns {boolean} - 验证结果\n   */\n  verifySignature(data, signature, publicKey) {\n    // 如果是对象，转换为JSON字符串\n    const dataStr = typeof data === \"object\" ? JSON.stringify(data) : data;\n    return SM2Crypto.verify(publicKey, dataStr, signature);\n  }\n};\nexport default CryptoUtils;", "map": {"version": 3, "names": ["SM2Crypto", "SM3Hasher", "SM4Crypto", "axios", "CryptoUtils", "getServerPublicKey", "response", "get", "data", "public<PERSON>ey", "error", "console", "Error", "encryptRequest", "serverPublicKey", "sm4Key", "<PERSON><PERSON>ey", "dataStr", "JSON", "stringify", "encryptResult", "encrypt", "encrypted<PERSON>ey", "encrypted_data", "ciphertext", "encrypted_key", "iv", "decryptResponse", "encryptedPackage", "privateKey", "decrypt", "decryptedData", "parse", "e", "generateSM2KeyPair", "generateKeyPair", "storePrivateKey", "password", "encryptedPrivateKey", "deriveKeyFromPassword", "localStorage", "setItem", "getStoredPrivateKey", "getItem", "hash", "substring", "hashPassword", "salt", "iterations", "hashWithSalt", "verifyPassword", "hashValue", "verify", "signData", "sign", "verifySignature", "signature"], "sources": ["C:/Users/<USER>/library_seat_system3/frontend/src/utils/cryptoUtils.js"], "sourcesContent": ["/**\n * 混合加密方案工具类\n * 提供前后端混合加密通信功能\n */\nimport { SM2Crypto, SM3Hasher, SM4Crypto } from \"./crypto\";\nimport axios from \"axios\";\n\n/**\n * 加密工具类\n */\nconst CryptoUtils = {\n  /**\n   * 获取服务器SM2公钥\n   * @returns {Promise<string>} 服务器公钥\n   */\n  async getServerPublicKey() {\n    try {\n      // 从API获取服务器公钥，避免硬编码\n      const response = await axios.get(\"/api/v1/crypto/server-public-key\");\n      return response.data.publicKey;\n    } catch (error) {\n      console.error(\"获取服务器公钥失败:\", error);\n      throw new Error(\"获取服务器公钥失败\");\n    }\n  },\n\n  /**\n   * 加密请求数据\n   * @param {Object|string} data - 原始数据\n   * @returns {Promise<Object>} - 加密后的数据包\n   */\n  async encryptRequest(data) {\n    try {\n      // 获取服务器公钥\n      const serverPublicKey = await this.getServerPublicKey();\n\n      // 生成随机SM4密钥\n      const sm4Key = SM4Crypto.generateKey();\n\n      // 如果是对象，转换为JSON字符串\n      const dataStr = typeof data === \"object\" ? JSON.stringify(data) : data;\n\n      // 使用SM4加密数据\n      const encryptResult = SM4Crypto.encrypt(sm4Key, dataStr);\n\n      // 使用SM2加密SM4密钥\n      const encryptedKey = SM2Crypto.encrypt(serverPublicKey, sm4Key);\n\n      // 返回加密包\n      return {\n        encrypted_data: encryptResult.ciphertext,\n        encrypted_key: encryptedKey,\n        iv: encryptResult.iv,\n      };\n    } catch (error) {\n      console.error(\"加密请求数据失败:\", error);\n      throw new Error(\"加密请求数据失败\");\n    }\n  },\n\n  /**\n   * 解密响应数据\n   * @param {Object} encryptedPackage - 加密包\n   * @param {string} privateKey - 客户端SM2私钥\n   * @returns {Object|string} - 解密后的数据\n   */\n  decryptResponse(encryptedPackage, privateKey) {\n    try {\n      // 提取加密数据\n      const { encrypted_data, encrypted_key, iv } = encryptedPackage;\n\n      if (!encrypted_data || !encrypted_key || !iv) {\n        throw new Error(\"加密包缺少必要字段\");\n      }\n\n      // 使用SM2解密SM4密钥\n      const sm4Key = SM2Crypto.decrypt(privateKey, encrypted_key);\n\n      // 使用SM4解密数据\n      const decryptedData = SM4Crypto.decrypt(sm4Key, encrypted_data, iv);\n\n      // 尝试解析JSON\n      try {\n        return JSON.parse(decryptedData);\n      } catch (e) {\n        return decryptedData;\n      }\n    } catch (error) {\n      console.error(\"解密响应数据失败:\", error);\n      throw new Error(\"解密响应数据失败\");\n    }\n  },\n\n  /**\n   * 生成SM2密钥对\n   * @returns {Object} - 包含公钥和私钥的对象\n   */\n  generateSM2KeyPair() {\n    return SM2Crypto.generateKeyPair();\n  },\n\n  /**\n   * 安全存储SM2私钥\n   * @param {string} privateKey - SM2私钥\n   * @param {string} password - 保护密码\n   */\n  storePrivateKey(privateKey, password) {\n    // 使用密码加密私钥后存储\n    const encryptedPrivateKey = SM4Crypto.encrypt(\n      this.deriveKeyFromPassword(password),\n      privateKey\n    ).ciphertext;\n    localStorage.setItem(\"sm2_private_key\", encryptedPrivateKey);\n  },\n\n  /**\n   * 获取存储的SM2私钥\n   * @param {string} password - 保护密码\n   * @returns {string} - SM2私钥\n   */\n  getStoredPrivateKey(password) {\n    try {\n      const encryptedPrivateKey = localStorage.getItem(\"sm2_private_key\");\n      if (!encryptedPrivateKey) {\n        throw new Error(\"未找到存储的私钥\");\n      }\n\n      // 使用密码解密私钥\n      return SM4Crypto.decrypt(\n        this.deriveKeyFromPassword(password),\n        encryptedPrivateKey\n      );\n    } catch (error) {\n      console.error(\"获取存储的私钥失败:\", error);\n      throw new Error(\"获取存储的私钥失败\");\n    }\n  },\n\n  /**\n   * 从密码派生密钥\n   * @param {string} password - 用户密码\n   * @returns {string} - 派生的密钥\n   */\n  deriveKeyFromPassword(password) {\n    // 简化实现，实际应使用PBKDF2等密钥派生函数\n    return SM3Hasher.hash(password).substring(0, 32);\n  },\n\n  /**\n   * 使用SM3哈希算法和盐值对密码进行哈希处理\n   * @param {string} password - 密码\n   * @param {string} salt - 盐值，如果为null则随机生成\n   * @param {number} iterations - 迭代次数\n   * @returns {Object} - 包含哈希值、盐值和迭代次数的对象\n   */\n  hashPassword(password, salt = null, iterations = 10000) {\n    return SM3Hasher.hashWithSalt(password, salt, iterations);\n  },\n\n  /**\n   * 验证密码是否匹配哈希值\n   * @param {string} password - 密码\n   * @param {string} hashValue - 哈希值(十六进制字符串)\n   * @param {string} salt - 盐值(十六进制字符串)\n   * @param {number} iterations - 迭代次数\n   * @returns {boolean} - 是否匹配\n   */\n  verifyPassword(password, hashValue, salt, iterations = 10000) {\n    return SM3Hasher.verify(password, hashValue, salt, iterations);\n  },\n\n  /**\n   * 使用SM2签名数据\n   * @param {Object|string} data - 待签名数据\n   * @param {string} privateKey - SM2私钥(十六进制字符串)\n   * @returns {string} - 签名(十六进制字符串)\n   */\n  signData(data, privateKey) {\n    // 如果是对象，转换为JSON字符串\n    const dataStr = typeof data === \"object\" ? JSON.stringify(data) : data;\n    return SM2Crypto.sign(privateKey, dataStr);\n  },\n\n  /**\n   * 验证SM2签名\n   * @param {Object|string} data - 原始数据\n   * @param {string} signature - 签名(十六进制字符串)\n   * @param {string} publicKey - SM2公钥(十六进制字符串)\n   * @returns {boolean} - 验证结果\n   */\n  verifySignature(data, signature, publicKey) {\n    // 如果是对象，转换为JSON字符串\n    const dataStr = typeof data === \"object\" ? JSON.stringify(data) : data;\n    return SM2Crypto.verify(publicKey, dataStr, signature);\n  },\n};\n\nexport default CryptoUtils;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,SAAS,EAAEC,SAAS,QAAQ,UAAU;AAC1D,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA;AACA,MAAMC,WAAW,GAAG;EAClB;AACF;AACA;AACA;EACE,MAAMC,kBAAkBA,CAAA,EAAG;IACzB,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMH,KAAK,CAACI,GAAG,CAAC,kCAAkC,CAAC;MACpE,OAAOD,QAAQ,CAACE,IAAI,CAACC,SAAS;IAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAM,IAAIE,KAAK,CAAC,WAAW,CAAC;IAC9B;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,cAAcA,CAACL,IAAI,EAAE;IACzB,IAAI;MACF;MACA,MAAMM,eAAe,GAAG,MAAM,IAAI,CAACT,kBAAkB,CAAC,CAAC;;MAEvD;MACA,MAAMU,MAAM,GAAGb,SAAS,CAACc,WAAW,CAAC,CAAC;;MAEtC;MACA,MAAMC,OAAO,GAAG,OAAOT,IAAI,KAAK,QAAQ,GAAGU,IAAI,CAACC,SAAS,CAACX,IAAI,CAAC,GAAGA,IAAI;;MAEtE;MACA,MAAMY,aAAa,GAAGlB,SAAS,CAACmB,OAAO,CAACN,MAAM,EAAEE,OAAO,CAAC;;MAExD;MACA,MAAMK,YAAY,GAAGtB,SAAS,CAACqB,OAAO,CAACP,eAAe,EAAEC,MAAM,CAAC;;MAE/D;MACA,OAAO;QACLQ,cAAc,EAAEH,aAAa,CAACI,UAAU;QACxCC,aAAa,EAAEH,YAAY;QAC3BI,EAAE,EAAEN,aAAa,CAACM;MACpB,CAAC;IACH,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAM,IAAIE,KAAK,CAAC,UAAU,CAAC;IAC7B;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEe,eAAeA,CAACC,gBAAgB,EAAEC,UAAU,EAAE;IAC5C,IAAI;MACF;MACA,MAAM;QAAEN,cAAc;QAAEE,aAAa;QAAEC;MAAG,CAAC,GAAGE,gBAAgB;MAE9D,IAAI,CAACL,cAAc,IAAI,CAACE,aAAa,IAAI,CAACC,EAAE,EAAE;QAC5C,MAAM,IAAId,KAAK,CAAC,WAAW,CAAC;MAC9B;;MAEA;MACA,MAAMG,MAAM,GAAGf,SAAS,CAAC8B,OAAO,CAACD,UAAU,EAAEJ,aAAa,CAAC;;MAE3D;MACA,MAAMM,aAAa,GAAG7B,SAAS,CAAC4B,OAAO,CAACf,MAAM,EAAEQ,cAAc,EAAEG,EAAE,CAAC;;MAEnE;MACA,IAAI;QACF,OAAOR,IAAI,CAACc,KAAK,CAACD,aAAa,CAAC;MAClC,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV,OAAOF,aAAa;MACtB;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAM,IAAIE,KAAK,CAAC,UAAU,CAAC;IAC7B;EACF,CAAC;EAED;AACF;AACA;AACA;EACEsB,kBAAkBA,CAAA,EAAG;IACnB,OAAOlC,SAAS,CAACmC,eAAe,CAAC,CAAC;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,eAAeA,CAACP,UAAU,EAAEQ,QAAQ,EAAE;IACpC;IACA,MAAMC,mBAAmB,GAAGpC,SAAS,CAACmB,OAAO,CAC3C,IAAI,CAACkB,qBAAqB,CAACF,QAAQ,CAAC,EACpCR,UACF,CAAC,CAACL,UAAU;IACZgB,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAEH,mBAAmB,CAAC;EAC9D,CAAC;EAED;AACF;AACA;AACA;AACA;EACEI,mBAAmBA,CAACL,QAAQ,EAAE;IAC5B,IAAI;MACF,MAAMC,mBAAmB,GAAGE,YAAY,CAACG,OAAO,CAAC,iBAAiB,CAAC;MACnE,IAAI,CAACL,mBAAmB,EAAE;QACxB,MAAM,IAAI1B,KAAK,CAAC,UAAU,CAAC;MAC7B;;MAEA;MACA,OAAOV,SAAS,CAAC4B,OAAO,CACtB,IAAI,CAACS,qBAAqB,CAACF,QAAQ,CAAC,EACpCC,mBACF,CAAC;IACH,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAM,IAAIE,KAAK,CAAC,WAAW,CAAC;IAC9B;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACE2B,qBAAqBA,CAACF,QAAQ,EAAE;IAC9B;IACA,OAAOpC,SAAS,CAAC2C,IAAI,CAACP,QAAQ,CAAC,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EAClD,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,YAAYA,CAACT,QAAQ,EAAEU,IAAI,GAAG,IAAI,EAAEC,UAAU,GAAG,KAAK,EAAE;IACtD,OAAO/C,SAAS,CAACgD,YAAY,CAACZ,QAAQ,EAAEU,IAAI,EAAEC,UAAU,CAAC;EAC3D,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,cAAcA,CAACb,QAAQ,EAAEc,SAAS,EAAEJ,IAAI,EAAEC,UAAU,GAAG,KAAK,EAAE;IAC5D,OAAO/C,SAAS,CAACmD,MAAM,CAACf,QAAQ,EAAEc,SAAS,EAAEJ,IAAI,EAAEC,UAAU,CAAC;EAChE,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEK,QAAQA,CAAC7C,IAAI,EAAEqB,UAAU,EAAE;IACzB;IACA,MAAMZ,OAAO,GAAG,OAAOT,IAAI,KAAK,QAAQ,GAAGU,IAAI,CAACC,SAAS,CAACX,IAAI,CAAC,GAAGA,IAAI;IACtE,OAAOR,SAAS,CAACsD,IAAI,CAACzB,UAAU,EAAEZ,OAAO,CAAC;EAC5C,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEsC,eAAeA,CAAC/C,IAAI,EAAEgD,SAAS,EAAE/C,SAAS,EAAE;IAC1C;IACA,MAAMQ,OAAO,GAAG,OAAOT,IAAI,KAAK,QAAQ,GAAGU,IAAI,CAACC,SAAS,CAACX,IAAI,CAAC,GAAGA,IAAI;IACtE,OAAOR,SAAS,CAACoD,MAAM,CAAC3C,SAAS,EAAEQ,OAAO,EAAEuC,SAAS,CAAC;EACxD;AACF,CAAC;AAED,eAAepD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}