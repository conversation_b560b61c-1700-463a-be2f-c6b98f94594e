{"ast": null, "code": "import { ref, reactive, onMounted, computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage } from \"element-plus\";\nimport { Location, CircleCheck, Close } from \"@element-plus/icons-vue\";\nimport { SM2Crypto } from \"@/utils/crypto\";\nexport default {\n  name: \"CheckInOut\",\n  components: {\n    Location,\n    CircleCheck,\n    Close\n  },\n  setup() {\n    const store = useStore();\n\n    // 响应式数据\n    const activeReservation = ref(null);\n    const checkingIn = ref(false);\n    const checkingOut = ref(false);\n    const cancelling = ref(false);\n\n    // 对话框状态\n    const checkInDialogVisible = ref(false);\n    const checkOutDialogVisible = ref(false);\n    const cancelDialogVisible = ref(false);\n\n    // 表单数据\n    const checkInForm = reactive({\n      useSignature: false\n    });\n    const checkOutForm = reactive({\n      useSignature: false\n    });\n\n    // 计算属性\n    const userHasPublicKey = computed(() => {\n      return store.getters[\"user/userInfo\"]?.public_key;\n    });\n\n    // 方法\n    const loadActiveReservation = async () => {\n      try {\n        const response = await store.dispatch(\"seat/getActiveReservation\");\n        activeReservation.value = response;\n      } catch (error) {\n        if (error.response?.status !== 404) {\n          ElMessage.error(\"获取活跃预约失败\");\n        }\n      }\n    };\n    const showCheckInDialog = () => {\n      checkInDialogVisible.value = true;\n    };\n    const showCheckOutDialog = () => {\n      checkOutDialogVisible.value = true;\n    };\n    const showCancelDialog = () => {\n      cancelDialogVisible.value = true;\n    };\n    const handleCheckIn = async () => {\n      try {\n        checkingIn.value = true;\n        const requestData = {\n          reservation_code: activeReservation.value.reservation_code\n        };\n\n        // 如果启用签名\n        if (checkInForm.useSignature && userHasPublicKey.value) {\n          const privateKey = localStorage.getItem(\"sm2_private_key\");\n          if (privateKey) {\n            const signature = SM2Crypto.sign(privateKey, activeReservation.value.reservation_code);\n            requestData.signature = signature;\n          }\n        }\n        await store.dispatch(\"seat/checkIn\", requestData);\n        ElMessage.success(\"签到成功\");\n        checkInDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        checkingIn.value = false;\n      }\n    };\n    const handleCheckOut = async () => {\n      try {\n        checkingOut.value = true;\n        const requestData = {\n          reservation_id: activeReservation.value.id\n        };\n\n        // 如果启用签名\n        if (checkOutForm.useSignature && userHasPublicKey.value) {\n          const privateKey = localStorage.getItem(\"sm2_private_key\");\n          if (privateKey) {\n            const signature = SM2Crypto.sign(privateKey, activeReservation.value.reservation_code);\n            requestData.signature = signature;\n          }\n        }\n        await store.dispatch(\"seat/checkOut\", requestData);\n        ElMessage.success(\"签退成功\");\n        checkOutDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        checkingOut.value = false;\n      }\n    };\n    const handleCancel = async () => {\n      try {\n        cancelling.value = true;\n        await store.dispatch(\"seat/cancelReservation\", {\n          id: activeReservation.value.id\n        });\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        cancelling.value = false;\n      }\n    };\n    const handleDialogClose = done => {\n      if (checkingIn.value || checkingOut.value || cancelling.value) {\n        ElMessage.warning(\"操作进行中，请稍候...\");\n        return;\n      }\n      done();\n    };\n    const getStatusType = status => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n    const getStatusText = status => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知\";\n      }\n    };\n    const formatDateTime = dateString => {\n      if (!dateString) return \"\";\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}-${String(date.getDate()).padStart(2, \"0\")} ${String(date.getHours()).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    };\n\n    // 生命周期\n    onMounted(() => {\n      loadActiveReservation();\n    });\n    return {\n      activeReservation,\n      checkingIn,\n      checkingOut,\n      cancelling,\n      checkInDialogVisible,\n      checkOutDialogVisible,\n      cancelDialogVisible,\n      checkInForm,\n      checkOutForm,\n      userHasPublicKey,\n      showCheckInDialog,\n      showCheckOutDialog,\n      showCancelDialog,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      handleDialogClose,\n      getStatusType,\n      getStatusText,\n      formatDateTime\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "computed", "useStore", "ElMessage", "Location", "CircleCheck", "Close", "SM2Crypto", "name", "components", "setup", "store", "activeReservation", "checkingIn", "checkingOut", "cancelling", "checkInDialogVisible", "checkOutDialogVisible", "cancelDialogVisible", "checkInForm", "useSignature", "checkOutForm", "userHasPublicKey", "getters", "public_key", "loadActiveReservation", "response", "dispatch", "value", "error", "status", "showCheckInDialog", "showCheckOutDialog", "showCancelDialog", "handleCheckIn", "requestData", "reservation_code", "privateKey", "localStorage", "getItem", "signature", "sign", "success", "message", "handleCheckOut", "reservation_id", "id", "handleCancel", "handleDialogClose", "done", "warning", "getStatusType", "getStatusText", "formatDateTime", "dateString", "date", "Date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\CheckInOut.vue"], "sourcesContent": ["<template>\n  <div class=\"checkin-checkout-container\">\n    <el-card class=\"main-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>签到签退</h2>\n        </div>\n      </template>\n\n      <!-- 当前活跃预约 -->\n      <div v-if=\"activeReservation\" class=\"active-reservation\">\n        <el-alert\n          :title=\"`当前预约: ${activeReservation.seat.room.name} - ${activeReservation.seat.seat_number}`\"\n          type=\"info\"\n          :closable=\"false\"\n          show-icon\n        >\n          <template #default>\n            <div class=\"reservation-info\">\n              <p>\n                <strong>预约时间:</strong>\n                {{ formatDateTime(activeReservation.start_time) }} -\n                {{ formatDateTime(activeReservation.end_time) }}\n              </p>\n              <p>\n                <strong>状态:</strong>\n                <el-tag :type=\"getStatusType(activeReservation.status)\">\n                  {{ getStatusText(activeReservation.status) }}\n                </el-tag>\n              </p>\n              <p v-if=\"activeReservation.check_in_time\">\n                <strong>签到时间:</strong>\n                {{ formatDateTime(activeReservation.check_in_time) }}\n              </p>\n            </div>\n          </template>\n        </el-alert>\n\n        <!-- 操作按钮 -->\n        <div class=\"action-buttons\">\n          <el-button\n            v-if=\"activeReservation.status === 'pending'\"\n            type=\"primary\"\n            size=\"large\"\n            :loading=\"checkingIn\"\n            @click=\"showCheckInDialog\"\n          >\n            <el-icon><Location /></el-icon>\n            签到\n          </el-button>\n\n          <el-button\n            v-if=\"activeReservation.status === 'checked_in'\"\n            type=\"success\"\n            size=\"large\"\n            :loading=\"checkingOut\"\n            @click=\"showCheckOutDialog\"\n          >\n            <el-icon><CircleCheck /></el-icon>\n            签退\n          </el-button>\n\n          <el-button\n            v-if=\"activeReservation.status === 'pending'\"\n            type=\"danger\"\n            size=\"large\"\n            :loading=\"cancelling\"\n            @click=\"showCancelDialog\"\n          >\n            <el-icon><Close /></el-icon>\n            取消预约\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 无活跃预约 -->\n      <div v-else class=\"no-reservation\">\n        <el-empty description=\"当前没有活跃的预约\">\n          <el-button type=\"primary\" @click=\"$router.push('/seat/reservation')\">\n            去预约座位\n          </el-button>\n        </el-empty>\n      </div>\n    </el-card>\n\n    <!-- 签到对话框 -->\n    <el-dialog\n      v-model=\"checkInDialogVisible\"\n      title=\"确认签到\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认签到到以下座位？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>预约时间:</strong>\n            {{ formatDateTime(activeReservation?.start_time) }} -\n            {{ formatDateTime(activeReservation?.end_time) }}\n          </p>\n        </div>\n\n        <!-- 签名选项 -->\n        <el-form\n          v-if=\"userHasPublicKey\"\n          :model=\"checkInForm\"\n          label-position=\"top\"\n        >\n          <el-form-item label=\"使用数字签名验证身份\">\n            <el-switch\n              v-model=\"checkInForm.useSignature\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n            />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"checkInDialogVisible = false\">取消</el-button>\n          <el-button\n            type=\"primary\"\n            :loading=\"checkingIn\"\n            @click=\"handleCheckIn\"\n          >\n            确认签到\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 签退对话框 -->\n    <el-dialog\n      v-model=\"checkOutDialogVisible\"\n      title=\"确认签退\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认签退以下座位？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>签到时间:</strong>\n            {{ formatDateTime(activeReservation?.check_in_time) }}\n          </p>\n        </div>\n\n        <!-- 签名选项 -->\n        <el-form\n          v-if=\"userHasPublicKey\"\n          :model=\"checkOutForm\"\n          label-position=\"top\"\n        >\n          <el-form-item label=\"使用数字签名验证身份\">\n            <el-switch\n              v-model=\"checkOutForm.useSignature\"\n              active-text=\"启用\"\n              inactive-text=\"禁用\"\n            />\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"checkOutDialogVisible = false\">取消</el-button>\n          <el-button\n            type=\"success\"\n            :loading=\"checkingOut\"\n            @click=\"handleCheckOut\"\n          >\n            确认签退\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n\n    <!-- 取消预约对话框 -->\n    <el-dialog\n      v-model=\"cancelDialogVisible\"\n      title=\"取消预约\"\n      width=\"400px\"\n      :before-close=\"handleDialogClose\"\n    >\n      <div class=\"dialog-content\">\n        <p>确认取消以下预约？</p>\n        <div class=\"seat-info\">\n          <p>\n            <strong>自习室:</strong> {{ activeReservation?.seat.room.name }}\n          </p>\n          <p>\n            <strong>座位:</strong> {{ activeReservation?.seat.seat_number }}\n          </p>\n          <p>\n            <strong>预约时间:</strong>\n            {{ formatDateTime(activeReservation?.start_time) }} -\n            {{ formatDateTime(activeReservation?.end_time) }}\n          </p>\n        </div>\n        <el-alert\n          title=\"注意：取消预约可能会影响您的信誉分\"\n          type=\"warning\"\n          :closable=\"false\"\n          show-icon\n        />\n      </div>\n\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">不取消</el-button>\n          <el-button type=\"danger\" :loading=\"cancelling\" @click=\"handleCancel\">\n            确认取消\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed } from \"vue\";\nimport { useStore } from \"vuex\";\nimport { ElMessage } from \"element-plus\";\nimport { Location, CircleCheck, Close } from \"@element-plus/icons-vue\";\nimport { SM2Crypto } from \"@/utils/crypto\";\n\nexport default {\n  name: \"CheckInOut\",\n  components: {\n    Location,\n    CircleCheck,\n    Close,\n  },\n  setup() {\n    const store = useStore();\n\n    // 响应式数据\n    const activeReservation = ref(null);\n    const checkingIn = ref(false);\n    const checkingOut = ref(false);\n    const cancelling = ref(false);\n\n    // 对话框状态\n    const checkInDialogVisible = ref(false);\n    const checkOutDialogVisible = ref(false);\n    const cancelDialogVisible = ref(false);\n\n    // 表单数据\n    const checkInForm = reactive({\n      useSignature: false,\n    });\n\n    const checkOutForm = reactive({\n      useSignature: false,\n    });\n\n    // 计算属性\n    const userHasPublicKey = computed(() => {\n      return store.getters[\"user/userInfo\"]?.public_key;\n    });\n\n    // 方法\n    const loadActiveReservation = async () => {\n      try {\n        const response = await store.dispatch(\"seat/getActiveReservation\");\n        activeReservation.value = response;\n      } catch (error) {\n        if (error.response?.status !== 404) {\n          ElMessage.error(\"获取活跃预约失败\");\n        }\n      }\n    };\n\n    const showCheckInDialog = () => {\n      checkInDialogVisible.value = true;\n    };\n\n    const showCheckOutDialog = () => {\n      checkOutDialogVisible.value = true;\n    };\n\n    const showCancelDialog = () => {\n      cancelDialogVisible.value = true;\n    };\n\n    const handleCheckIn = async () => {\n      try {\n        checkingIn.value = true;\n\n        const requestData = {\n          reservation_code: activeReservation.value.reservation_code,\n        };\n\n        // 如果启用签名\n        if (checkInForm.useSignature && userHasPublicKey.value) {\n          const privateKey = localStorage.getItem(\"sm2_private_key\");\n          if (privateKey) {\n            const signature = SM2Crypto.sign(\n              privateKey,\n              activeReservation.value.reservation_code\n            );\n            requestData.signature = signature;\n          }\n        }\n\n        await store.dispatch(\"seat/checkIn\", requestData);\n\n        ElMessage.success(\"签到成功\");\n        checkInDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"签到失败\");\n      } finally {\n        checkingIn.value = false;\n      }\n    };\n\n    const handleCheckOut = async () => {\n      try {\n        checkingOut.value = true;\n\n        const requestData = {\n          reservation_id: activeReservation.value.id,\n        };\n\n        // 如果启用签名\n        if (checkOutForm.useSignature && userHasPublicKey.value) {\n          const privateKey = localStorage.getItem(\"sm2_private_key\");\n          if (privateKey) {\n            const signature = SM2Crypto.sign(\n              privateKey,\n              activeReservation.value.reservation_code\n            );\n            requestData.signature = signature;\n          }\n        }\n\n        await store.dispatch(\"seat/checkOut\", requestData);\n\n        ElMessage.success(\"签退成功\");\n        checkOutDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"签退失败\");\n      } finally {\n        checkingOut.value = false;\n      }\n    };\n\n    const handleCancel = async () => {\n      try {\n        cancelling.value = true;\n\n        await store.dispatch(\"seat/cancelReservation\", {\n          id: activeReservation.value.id,\n        });\n\n        ElMessage.success(\"预约已取消\");\n        cancelDialogVisible.value = false;\n        await loadActiveReservation();\n      } catch (error) {\n        ElMessage.error(error.message || \"取消预约失败\");\n      } finally {\n        cancelling.value = false;\n      }\n    };\n\n    const handleDialogClose = (done) => {\n      if (checkingIn.value || checkingOut.value || cancelling.value) {\n        ElMessage.warning(\"操作进行中，请稍候...\");\n        return;\n      }\n      done();\n    };\n\n    const getStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    const getStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知\";\n      }\n    };\n\n    const formatDateTime = (dateString) => {\n      if (!dateString) return \"\";\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")} ${String(\n        date.getHours()\n      ).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    };\n\n    // 生命周期\n    onMounted(() => {\n      loadActiveReservation();\n    });\n\n    return {\n      activeReservation,\n      checkingIn,\n      checkingOut,\n      cancelling,\n      checkInDialogVisible,\n      checkOutDialogVisible,\n      cancelDialogVisible,\n      checkInForm,\n      checkOutForm,\n      userHasPublicKey,\n      showCheckInDialog,\n      showCheckOutDialog,\n      showCancelDialog,\n      handleCheckIn,\n      handleCheckOut,\n      handleCancel,\n      handleDialogClose,\n      getStatusType,\n      getStatusText,\n      formatDateTime,\n    };\n  },\n};\n</script>\n\n<style scoped>\n.checkin-checkout-container {\n  padding: 20px;\n}\n\n.main-card {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.card-header h2 {\n  margin: 0;\n  color: #303133;\n}\n\n.active-reservation {\n  margin-bottom: 20px;\n}\n\n.reservation-info {\n  margin-top: 10px;\n}\n\n.reservation-info p {\n  margin: 5px 0;\n  color: #606266;\n}\n\n.action-buttons {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.action-buttons .el-button {\n  margin: 0 10px;\n  min-width: 120px;\n}\n\n.no-reservation {\n  text-align: center;\n  padding: 40px 0;\n}\n\n.dialog-content {\n  text-align: center;\n}\n\n.seat-info {\n  background-color: #f5f7fa;\n  padding: 15px;\n  border-radius: 4px;\n  margin: 15px 0;\n  text-align: left;\n}\n\n.seat-info p {\n  margin: 5px 0;\n  color: #606266;\n}\n\n.dialog-footer {\n  text-align: right;\n}\n</style>\n"], "mappings": "AAyOA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAK;AACxD,SAASC,QAAO,QAAS,MAAM;AAC/B,SAASC,SAAQ,QAAS,cAAc;AACxC,SAASC,QAAQ,EAAEC,WAAW,EAAEC,KAAI,QAAS,yBAAyB;AACtE,SAASC,SAAQ,QAAS,gBAAgB;AAE1C,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVL,QAAQ;IACRC,WAAW;IACXC;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIT,QAAQ,CAAC,CAAC;;IAExB;IACA,MAAMU,iBAAgB,GAAId,GAAG,CAAC,IAAI,CAAC;IACnC,MAAMe,UAAS,GAAIf,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMgB,WAAU,GAAIhB,GAAG,CAAC,KAAK,CAAC;IAC9B,MAAMiB,UAAS,GAAIjB,GAAG,CAAC,KAAK,CAAC;;IAE7B;IACA,MAAMkB,oBAAmB,GAAIlB,GAAG,CAAC,KAAK,CAAC;IACvC,MAAMmB,qBAAoB,GAAInB,GAAG,CAAC,KAAK,CAAC;IACxC,MAAMoB,mBAAkB,GAAIpB,GAAG,CAAC,KAAK,CAAC;;IAEtC;IACA,MAAMqB,WAAU,GAAIpB,QAAQ,CAAC;MAC3BqB,YAAY,EAAE;IAChB,CAAC,CAAC;IAEF,MAAMC,YAAW,GAAItB,QAAQ,CAAC;MAC5BqB,YAAY,EAAE;IAChB,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAe,GAAIrB,QAAQ,CAAC,MAAM;MACtC,OAAOU,KAAK,CAACY,OAAO,CAAC,eAAe,CAAC,EAAEC,UAAU;IACnD,CAAC,CAAC;;IAEF;IACA,MAAMC,qBAAoB,GAAI,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMf,KAAK,CAACgB,QAAQ,CAAC,2BAA2B,CAAC;QAClEf,iBAAiB,CAACgB,KAAI,GAAIF,QAAQ;MACpC,EAAE,OAAOG,KAAK,EAAE;QACd,IAAIA,KAAK,CAACH,QAAQ,EAAEI,MAAK,KAAM,GAAG,EAAE;UAClC3B,SAAS,CAAC0B,KAAK,CAAC,UAAU,CAAC;QAC7B;MACF;IACF,CAAC;IAED,MAAME,iBAAgB,GAAIA,CAAA,KAAM;MAC9Bf,oBAAoB,CAACY,KAAI,GAAI,IAAI;IACnC,CAAC;IAED,MAAMI,kBAAiB,GAAIA,CAAA,KAAM;MAC/Bf,qBAAqB,CAACW,KAAI,GAAI,IAAI;IACpC,CAAC;IAED,MAAMK,gBAAe,GAAIA,CAAA,KAAM;MAC7Bf,mBAAmB,CAACU,KAAI,GAAI,IAAI;IAClC,CAAC;IAED,MAAMM,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFrB,UAAU,CAACe,KAAI,GAAI,IAAI;QAEvB,MAAMO,WAAU,GAAI;UAClBC,gBAAgB,EAAExB,iBAAiB,CAACgB,KAAK,CAACQ;QAC5C,CAAC;;QAED;QACA,IAAIjB,WAAW,CAACC,YAAW,IAAKE,gBAAgB,CAACM,KAAK,EAAE;UACtD,MAAMS,UAAS,GAAIC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;UAC1D,IAAIF,UAAU,EAAE;YACd,MAAMG,SAAQ,GAAIjC,SAAS,CAACkC,IAAI,CAC9BJ,UAAU,EACVzB,iBAAiB,CAACgB,KAAK,CAACQ,gBAC1B,CAAC;YACDD,WAAW,CAACK,SAAQ,GAAIA,SAAS;UACnC;QACF;QAEA,MAAM7B,KAAK,CAACgB,QAAQ,CAAC,cAAc,EAAEQ,WAAW,CAAC;QAEjDhC,SAAS,CAACuC,OAAO,CAAC,MAAM,CAAC;QACzB1B,oBAAoB,CAACY,KAAI,GAAI,KAAK;QAClC,MAAMH,qBAAqB,CAAC,CAAC;MAC/B,EAAE,OAAOI,KAAK,EAAE;QACd1B,SAAS,CAAC0B,KAAK,CAACA,KAAK,CAACc,OAAM,IAAK,MAAM,CAAC;MAC1C,UAAU;QACR9B,UAAU,CAACe,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;IAED,MAAMgB,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF9B,WAAW,CAACc,KAAI,GAAI,IAAI;QAExB,MAAMO,WAAU,GAAI;UAClBU,cAAc,EAAEjC,iBAAiB,CAACgB,KAAK,CAACkB;QAC1C,CAAC;;QAED;QACA,IAAIzB,YAAY,CAACD,YAAW,IAAKE,gBAAgB,CAACM,KAAK,EAAE;UACvD,MAAMS,UAAS,GAAIC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;UAC1D,IAAIF,UAAU,EAAE;YACd,MAAMG,SAAQ,GAAIjC,SAAS,CAACkC,IAAI,CAC9BJ,UAAU,EACVzB,iBAAiB,CAACgB,KAAK,CAACQ,gBAC1B,CAAC;YACDD,WAAW,CAACK,SAAQ,GAAIA,SAAS;UACnC;QACF;QAEA,MAAM7B,KAAK,CAACgB,QAAQ,CAAC,eAAe,EAAEQ,WAAW,CAAC;QAElDhC,SAAS,CAACuC,OAAO,CAAC,MAAM,CAAC;QACzBzB,qBAAqB,CAACW,KAAI,GAAI,KAAK;QACnC,MAAMH,qBAAqB,CAAC,CAAC;MAC/B,EAAE,OAAOI,KAAK,EAAE;QACd1B,SAAS,CAAC0B,KAAK,CAACA,KAAK,CAACc,OAAM,IAAK,MAAM,CAAC;MAC1C,UAAU;QACR7B,WAAW,CAACc,KAAI,GAAI,KAAK;MAC3B;IACF,CAAC;IAED,MAAMmB,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFhC,UAAU,CAACa,KAAI,GAAI,IAAI;QAEvB,MAAMjB,KAAK,CAACgB,QAAQ,CAAC,wBAAwB,EAAE;UAC7CmB,EAAE,EAAElC,iBAAiB,CAACgB,KAAK,CAACkB;QAC9B,CAAC,CAAC;QAEF3C,SAAS,CAACuC,OAAO,CAAC,OAAO,CAAC;QAC1BxB,mBAAmB,CAACU,KAAI,GAAI,KAAK;QACjC,MAAMH,qBAAqB,CAAC,CAAC;MAC/B,EAAE,OAAOI,KAAK,EAAE;QACd1B,SAAS,CAAC0B,KAAK,CAACA,KAAK,CAACc,OAAM,IAAK,QAAQ,CAAC;MAC5C,UAAU;QACR5B,UAAU,CAACa,KAAI,GAAI,KAAK;MAC1B;IACF,CAAC;IAED,MAAMoB,iBAAgB,GAAKC,IAAI,IAAK;MAClC,IAAIpC,UAAU,CAACe,KAAI,IAAKd,WAAW,CAACc,KAAI,IAAKb,UAAU,CAACa,KAAK,EAAE;QAC7DzB,SAAS,CAAC+C,OAAO,CAAC,cAAc,CAAC;QACjC;MACF;MACAD,IAAI,CAAC,CAAC;IACR,CAAC;IAED,MAAME,aAAY,GAAKrB,MAAM,IAAK;MAChC,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,SAAS;QAClB,KAAK,YAAY;UACf,OAAO,SAAS;QAClB,KAAK,WAAW;UACd,OAAO,MAAM;QACf,KAAK,WAAW;UACd,OAAO,QAAQ;QACjB,KAAK,SAAS;UACZ,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF,CAAC;IAED,MAAMsB,aAAY,GAAKtB,MAAM,IAAK;MAChC,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,KAAK;QACd,KAAK,YAAY;UACf,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,SAAS;UACZ,OAAO,KAAK;QACd;UACE,OAAO,IAAI;MACf;IACF,CAAC;IAED,MAAMuB,cAAa,GAAKC,UAAU,IAAK;MACrC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,IAAI,CAACI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAClE,CAAC,EACD,GACF,CAAC,IAAIF,MAAM,CAACH,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CACpDH,IAAI,CAACO,QAAQ,CAAC,CAChB,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CAACH,IAAI,CAACQ,UAAU,CAAC,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IACpE,CAAC;;IAED;IACA5D,SAAS,CAAC,MAAM;MACdyB,qBAAqB,CAAC,CAAC;IACzB,CAAC,CAAC;IAEF,OAAO;MACLb,iBAAiB;MACjBC,UAAU;MACVC,WAAW;MACXC,UAAU;MACVC,oBAAoB;MACpBC,qBAAqB;MACrBC,mBAAmB;MACnBC,WAAW;MACXE,YAAY;MACZC,gBAAgB;MAChBS,iBAAiB;MACjBC,kBAAkB;MAClBC,gBAAgB;MAChBC,aAAa;MACbU,cAAc;MACdG,YAAY;MACZC,iBAAiB;MACjBG,aAAa;MACbC,aAAa;MACbC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}