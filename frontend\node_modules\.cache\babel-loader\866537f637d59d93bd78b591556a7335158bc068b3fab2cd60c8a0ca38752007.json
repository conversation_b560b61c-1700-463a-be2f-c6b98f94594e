{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, computed, onMounted } from \"vue\";\nimport { useRouter } from \"vue-router\";\nimport { useStore } from \"vuex\";\n// 导入API\nimport seatApi from \"@/api/seat\";\nimport userApi from \"@/api/user\";\nexport default {\n  name: \"DashboardView\",\n  setup() {\n    const router = useRouter();\n    const store = useStore();\n\n    // 统计数据\n    const availableSeats = ref(0);\n    const totalSeats = ref(0);\n    const creditScore = ref(0);\n    const loading = ref(true);\n\n    // 计算信誉分状态\n    const creditScoreType = computed(() => {\n      if (creditScore.value >= 90) return \"success\";\n      if (creditScore.value >= 70) return \"warning\";\n      return \"danger\";\n    });\n    const creditScoreText = computed(() => {\n      if (creditScore.value >= 90) return \"优秀\";\n      if (creditScore.value >= 70) return \"良好\";\n      if (creditScore.value >= 50) return \"一般\";\n      return \"较差\";\n    });\n\n    // 最近预约\n    const recentReservations = ref([]);\n\n    // 加载统计数据\n    const loadStatistics = async () => {\n      try {\n        loading.value = true;\n\n        // 获取自习室列表计算座位统计\n        const roomsResponse = await seatApi.getRooms();\n        const rooms = roomsResponse.data.results || roomsResponse.data;\n        totalSeats.value = rooms.reduce((sum, room) => sum + room.capacity, 0);\n        availableSeats.value = rooms.reduce((sum, room) => sum + (room.available_seats || 0), 0);\n\n        // 获取用户信息\n        const userResponse = await userApi.getUserInfo();\n        creditScore.value = userResponse.data.credit_score || 0;\n\n        // 获取最近预约\n        const reservationsResponse = await seatApi.getMyReservations();\n        const allReservations = reservationsResponse.data.results || reservationsResponse.data;\n        recentReservations.value = allReservations.slice(0, 3);\n      } catch (error) {\n        console.error(\"加载统计数据失败:\", error);\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 跳转到自习室列表\n    const goToRooms = () => {\n      router.push(\"/seat/rooms\");\n    };\n\n    // 跳转到我的预约\n    const goToMyReservations = () => {\n      router.push(\"/user/reservations\");\n    };\n\n    // 跳转到信誉分记录\n    const goToCreditRecords = () => {\n      router.push(\"/user/credit-records\");\n    };\n\n    // 获取预约状态类型\n    const getStatusType = status => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getStatusText = status => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知\";\n      }\n    };\n\n    // 获取进度条状态\n    const getProgressStatus = (available, total) => {\n      const percentage = available / total * 100;\n      if (percentage < 20) return \"exception\";\n      if (percentage < 50) return \"warning\";\n      return \"success\";\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, \"0\")}-${String(date.getDate()).padStart(2, \"0\")}`;\n    };\n\n    // 格式化时间\n    const formatTime = dateString => {\n      const date = new Date(dateString);\n      return `${String(date.getHours()).padStart(2, \"0\")}:${String(date.getMinutes()).padStart(2, \"0\")}`;\n    };\n\n    // 查看预约详情\n    const viewReservation = id => {\n      router.push(`/seat/reservation/${id}`);\n    };\n    onMounted(() => {\n      loadStatistics();\n    });\n    return {\n      recentReservations,\n      availableSeats,\n      totalSeats,\n      creditScore,\n      creditScoreType,\n      creditScoreText,\n      loading,\n      getStatusType,\n      getStatusText,\n      getProgressStatus,\n      formatDate,\n      formatTime,\n      viewReservation,\n      goToRooms,\n      goToMyReservations,\n      goToCreditRecords\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "useRouter", "useStore", "seatApi", "userApi", "name", "setup", "router", "store", "availableSeats", "totalSeats", "creditScore", "loading", "creditScoreType", "value", "creditScoreText", "recentReservations", "loadStatistics", "roomsResponse", "getRooms", "rooms", "data", "results", "reduce", "sum", "room", "capacity", "available_seats", "userResponse", "getUserInfo", "credit_score", "reservationsResponse", "getMyReservations", "allReservations", "slice", "error", "console", "goToRooms", "push", "goToMyReservations", "goToCreditRecords", "getStatusType", "status", "getStatusText", "getProgressStatus", "available", "total", "percentage", "formatDate", "dateString", "date", "Date", "getFullYear", "String", "getMonth", "padStart", "getDate", "formatTime", "getHours", "getMinutes", "viewReservation", "id"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"16\">\n        <el-card class=\"welcome-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h2>欢迎使用基于国密算法的图书馆自习室座位管理系统</h2>\n            </div>\n          </template>\n          <div class=\"dashboard-content\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"当前可用座位\" :value=\"availableSeats\">\n                  <template #suffix>\n                    <span class=\"statistic-suffix\">/ {{ totalSeats }}</span>\n                  </template>\n                </el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"我的预约\" :value=\"recentReservations.length\">\n                  <template #suffix>\n                    <span class=\"statistic-suffix\">个</span>\n                  </template>\n                </el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"我的信誉分\" :value=\"creditScore\">\n                  <template #suffix>\n                    <el-tag :type=\"creditScoreType\" size=\"small\">{{\n                      creditScoreText\n                    }}</el-tag>\n                  </template>\n                </el-statistic>\n              </el-col>\n            </el-row>\n\n            <div class=\"quick-actions\">\n              <h3>快捷操作</h3>\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"Plus\"\n                    @click=\"$router.push('/seat/rooms')\"\n                    class=\"action-button\"\n                  >\n                    预约座位\n                  </el-button>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-button\n                    type=\"success\"\n                    icon=\"Location\"\n                    @click=\"$router.push('/seat/checkin')\"\n                    class=\"action-button\"\n                  >\n                    签到签退\n                  </el-button>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-button\n                    type=\"info\"\n                    icon=\"List\"\n                    @click=\"$router.push('/user/reservations')\"\n                    class=\"action-button\"\n                  >\n                    我的预约\n                  </el-button>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-button\n                    type=\"warning\"\n                    icon=\"View\"\n                    @click=\"$router.push('/seat/map')\"\n                    class=\"action-button\"\n                  >\n                    座位地图\n                  </el-button>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </el-card>\n\n        <!-- 最近预约 -->\n        <el-card class=\"recent-reservations-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>我的最近预约</h3>\n              <el-button\n                type=\"primary\"\n                link\n                @click=\"$router.push('/user/reservations')\"\n              >\n                查看全部\n              </el-button>\n            </div>\n          </template>\n          <div v-if=\"recentReservations.length > 0\">\n            <el-table :data=\"recentReservations\" style=\"width: 100%\">\n              <el-table-column prop=\"roomName\" label=\"自习室\" width=\"180\" />\n              <el-table-column prop=\"seatNumber\" label=\"座位号\" width=\"100\" />\n              <el-table-column label=\"时间\">\n                <template #default=\"scope\">\n                  {{ formatDate(scope.row.startTime) }}\n                  {{ formatTime(scope.row.startTime) }} -\n                  {{ formatTime(scope.row.endTime) }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"状态\" width=\"100\">\n                <template #default=\"scope\">\n                  <el-tag :type=\"getStatusType(scope.row.status)\">\n                    {{ getStatusText(scope.row.status) }}\n                  </el-tag>\n                </template>\n              </el-table-column>\n              <el-table-column label=\"操作\" width=\"120\">\n                <template #default=\"scope\">\n                  <el-button\n                    link\n                    type=\"primary\"\n                    @click=\"viewReservation(scope.row.id)\"\n                  >\n                    查看\n                  </el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n          <el-empty v-else description=\"暂无预约记录\" />\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"8\">\n        <!-- 自习室状态 -->\n        <el-card class=\"room-status-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>自习室状态</h3>\n            </div>\n          </template>\n          <div class=\"room-status-list\">\n            <el-scrollbar height=\"200px\">\n              <div\n                v-for=\"room in rooms.slice(0, 6)\"\n                :key=\"room.id\"\n                class=\"room-status-item\"\n                @click=\"$router.push(`/seat/map?roomId=${room.id}`)\"\n              >\n                <div class=\"room-info\">\n                  <div class=\"room-name\">{{ room.name }}</div>\n                  <div class=\"room-capacity\">\n                    可用:\n                    <span class=\"available-seats\">{{\n                      room.availableSeats\n                    }}</span>\n                    / {{ room.capacity }}\n                  </div>\n                </div>\n                <el-progress\n                  :percentage=\"\n                    Math.round((room.availableSeats / room.capacity) * 100)\n                  \"\n                  :status=\"\n                    getProgressStatus(room.availableSeats, room.capacity)\n                  \"\n                />\n              </div>\n            </el-scrollbar>\n          </div>\n        </el-card>\n\n        <!-- 系统公告 -->\n        <el-card class=\"announcement-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>系统公告</h3>\n            </div>\n          </template>\n          <div class=\"announcement-list\">\n            <div class=\"announcement-item\">\n              <div class=\"announcement-title\">系统升级通知</div>\n              <div class=\"announcement-time\">2023-05-15</div>\n              <div class=\"announcement-content\">\n                系统将于本周六凌晨2:00-4:00进行升级维护，期间系统将暂停服务，请提前做好安排。\n              </div>\n            </div>\n            <el-divider />\n            <div class=\"announcement-item\">\n              <div class=\"announcement-title\">图书馆开放时间调整</div>\n              <div class=\"announcement-time\">2023-05-10</div>\n              <div class=\"announcement-content\">\n                自5月15日起，图书馆开放时间调整为8:00-22:00，请知悉。\n              </div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from \"vue\";\nimport { useRouter } from \"vue-router\";\nimport { useStore } from \"vuex\";\n// 导入API\nimport seatApi from \"@/api/seat\";\nimport userApi from \"@/api/user\";\n\nexport default {\n  name: \"DashboardView\",\n  setup() {\n    const router = useRouter();\n    const store = useStore();\n\n    // 统计数据\n    const availableSeats = ref(0);\n    const totalSeats = ref(0);\n    const creditScore = ref(0);\n    const loading = ref(true);\n\n    // 计算信誉分状态\n    const creditScoreType = computed(() => {\n      if (creditScore.value >= 90) return \"success\";\n      if (creditScore.value >= 70) return \"warning\";\n      return \"danger\";\n    });\n\n    const creditScoreText = computed(() => {\n      if (creditScore.value >= 90) return \"优秀\";\n      if (creditScore.value >= 70) return \"良好\";\n      if (creditScore.value >= 50) return \"一般\";\n      return \"较差\";\n    });\n\n    // 最近预约\n    const recentReservations = ref([]);\n\n    // 加载统计数据\n    const loadStatistics = async () => {\n      try {\n        loading.value = true;\n\n        // 获取自习室列表计算座位统计\n        const roomsResponse = await seatApi.getRooms();\n        const rooms = roomsResponse.data.results || roomsResponse.data;\n\n        totalSeats.value = rooms.reduce((sum, room) => sum + room.capacity, 0);\n        availableSeats.value = rooms.reduce((sum, room) => sum + (room.available_seats || 0), 0);\n\n        // 获取用户信息\n        const userResponse = await userApi.getUserInfo();\n        creditScore.value = userResponse.data.credit_score || 0;\n\n        // 获取最近预约\n        const reservationsResponse = await seatApi.getMyReservations();\n        const allReservations = reservationsResponse.data.results || reservationsResponse.data;\n        recentReservations.value = allReservations.slice(0, 3);\n\n      } catch (error) {\n        console.error(\"加载统计数据失败:\", error);\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 跳转到自习室列表\n    const goToRooms = () => {\n      router.push(\"/seat/rooms\");\n    };\n\n    // 跳转到我的预约\n    const goToMyReservations = () => {\n      router.push(\"/user/reservations\");\n    };\n\n    // 跳转到信誉分记录\n    const goToCreditRecords = () => {\n      router.push(\"/user/credit-records\");\n    };\n\n    // 获取预约状态类型\n    const getStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知\";\n      }\n    };\n\n    // 获取进度条状态\n    const getProgressStatus = (available, total) => {\n      const percentage = (available / total) * 100;\n      if (percentage < 20) return \"exception\";\n      if (percentage < 50) return \"warning\";\n      return \"success\";\n    };\n\n    // 格式化日期\n    const formatDate = (dateString) => {\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")}`;\n    };\n\n    // 格式化时间\n    const formatTime = (dateString) => {\n      const date = new Date(dateString);\n      return `${String(date.getHours()).padStart(2, \"0\")}:${String(\n        date.getMinutes()\n      ).padStart(2, \"0\")}`;\n    };\n\n    // 查看预约详情\n    const viewReservation = (id) => {\n      router.push(`/seat/reservation/${id}`);\n    };\n\n    onMounted(() => {\n      loadStatistics();\n    });\n\n    return {\n      recentReservations,\n      availableSeats,\n      totalSeats,\n      creditScore,\n      creditScoreType,\n      creditScoreText,\n      loading,\n      getStatusType,\n      getStatusText,\n      getProgressStatus,\n      formatDate,\n      formatTime,\n      viewReservation,\n      goToRooms,\n      goToMyReservations,\n      goToCreditRecords,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-container {\n  padding: 20px;\n}\n\n.welcome-card,\n.recent-reservations-card,\n.room-status-card,\n.announcement-card {\n  margin-bottom: 20px;\n  transition: all 0.3s;\n\n  &:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h2,\n  h3 {\n    margin: 0;\n  }\n}\n\n.dashboard-content {\n  padding: 20px 0;\n}\n\n.statistic-suffix {\n  font-size: 14px;\n  color: #909399;\n  margin-left: 5px;\n}\n\n.quick-actions {\n  margin-top: 30px;\n\n  h3 {\n    margin-bottom: 20px;\n    font-size: 18px;\n    color: #303133;\n  }\n\n  .action-button {\n    width: 100%;\n    height: 50px;\n    font-size: 16px;\n    transition: all 0.3s;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n  }\n}\n\n.room-status-list {\n  .room-status-item {\n    padding: 10px;\n    margin-bottom: 15px;\n    border-radius: 4px;\n    background-color: #f5f7fa;\n    cursor: pointer;\n    transition: all 0.3s;\n\n    &:hover {\n      background-color: #ecf5ff;\n    }\n\n    .room-info {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n\n      .room-name {\n        font-weight: bold;\n      }\n\n      .room-capacity {\n        font-size: 14px;\n        color: #606266;\n\n        .available-seats {\n          color: #67c23a;\n          font-weight: bold;\n        }\n      }\n    }\n  }\n}\n\n.announcement-list {\n  .announcement-item {\n    margin-bottom: 15px;\n\n    .announcement-title {\n      font-weight: bold;\n      font-size: 16px;\n      margin-bottom: 5px;\n    }\n\n    .announcement-time {\n      font-size: 12px;\n      color: #909399;\n      margin-bottom: 8px;\n    }\n\n    .announcement-content {\n      font-size: 14px;\n      color: #606266;\n      line-height: 1.5;\n    }\n  }\n}\n\n.recent-reservations-card {\n  margin-top: 20px;\n}\n</style>\n"], "mappings": ";;;AA4MA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAK;AAC9C,SAASC,SAAQ,QAAS,YAAY;AACtC,SAASC,QAAO,QAAS,MAAM;AAC/B;AACA,OAAOC,OAAM,MAAO,YAAY;AAChC,OAAOC,OAAM,MAAO,YAAY;AAEhC,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIN,SAAS,CAAC,CAAC;IAC1B,MAAMO,KAAI,GAAIN,QAAQ,CAAC,CAAC;;IAExB;IACA,MAAMO,cAAa,GAAIX,GAAG,CAAC,CAAC,CAAC;IAC7B,MAAMY,UAAS,GAAIZ,GAAG,CAAC,CAAC,CAAC;IACzB,MAAMa,WAAU,GAAIb,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMc,OAAM,GAAId,GAAG,CAAC,IAAI,CAAC;;IAEzB;IACA,MAAMe,eAAc,GAAId,QAAQ,CAAC,MAAM;MACrC,IAAIY,WAAW,CAACG,KAAI,IAAK,EAAE,EAAE,OAAO,SAAS;MAC7C,IAAIH,WAAW,CAACG,KAAI,IAAK,EAAE,EAAE,OAAO,SAAS;MAC7C,OAAO,QAAQ;IACjB,CAAC,CAAC;IAEF,MAAMC,eAAc,GAAIhB,QAAQ,CAAC,MAAM;MACrC,IAAIY,WAAW,CAACG,KAAI,IAAK,EAAE,EAAE,OAAO,IAAI;MACxC,IAAIH,WAAW,CAACG,KAAI,IAAK,EAAE,EAAE,OAAO,IAAI;MACxC,IAAIH,WAAW,CAACG,KAAI,IAAK,EAAE,EAAE,OAAO,IAAI;MACxC,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA,MAAME,kBAAiB,GAAIlB,GAAG,CAAC,EAAE,CAAC;;IAElC;IACA,MAAMmB,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFL,OAAO,CAACE,KAAI,GAAI,IAAI;;QAEpB;QACA,MAAMI,aAAY,GAAI,MAAMf,OAAO,CAACgB,QAAQ,CAAC,CAAC;QAC9C,MAAMC,KAAI,GAAIF,aAAa,CAACG,IAAI,CAACC,OAAM,IAAKJ,aAAa,CAACG,IAAI;QAE9DX,UAAU,CAACI,KAAI,GAAIM,KAAK,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAE,GAAIC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;QACtEjB,cAAc,CAACK,KAAI,GAAIM,KAAK,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAE,IAAKC,IAAI,CAACE,eAAc,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;QAExF;QACA,MAAMC,YAAW,GAAI,MAAMxB,OAAO,CAACyB,WAAW,CAAC,CAAC;QAChDlB,WAAW,CAACG,KAAI,GAAIc,YAAY,CAACP,IAAI,CAACS,YAAW,IAAK,CAAC;;QAEvD;QACA,MAAMC,oBAAmB,GAAI,MAAM5B,OAAO,CAAC6B,iBAAiB,CAAC,CAAC;QAC9D,MAAMC,eAAc,GAAIF,oBAAoB,CAACV,IAAI,CAACC,OAAM,IAAKS,oBAAoB,CAACV,IAAI;QACtFL,kBAAkB,CAACF,KAAI,GAAImB,eAAe,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAExD,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC,UAAU;QACRvB,OAAO,CAACE,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMuB,SAAQ,GAAIA,CAAA,KAAM;MACtB9B,MAAM,CAAC+B,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;;IAED;IACA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/BhC,MAAM,CAAC+B,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;;IAED;IACA,MAAME,iBAAgB,GAAIA,CAAA,KAAM;MAC9BjC,MAAM,CAAC+B,IAAI,CAAC,sBAAsB,CAAC;IACrC,CAAC;;IAED;IACA,MAAMG,aAAY,GAAKC,MAAM,IAAK;MAChC,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,SAAS;QAClB,KAAK,YAAY;UACf,OAAO,SAAS;QAClB,KAAK,WAAW;UACd,OAAO,MAAM;QACf,KAAK,WAAW;UACd,OAAO,QAAQ;QACjB,KAAK,SAAS;UACZ,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMC,aAAY,GAAKD,MAAM,IAAK;MAChC,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,KAAK;QACd,KAAK,YAAY;UACf,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,SAAS;UACZ,OAAO,KAAK;QACd;UACE,OAAO,IAAI;MACf;IACF,CAAC;;IAED;IACA,MAAME,iBAAgB,GAAIA,CAACC,SAAS,EAAEC,KAAK,KAAK;MAC9C,MAAMC,UAAS,GAAKF,SAAQ,GAAIC,KAAK,GAAI,GAAG;MAC5C,IAAIC,UAAS,GAAI,EAAE,EAAE,OAAO,WAAW;MACvC,IAAIA,UAAS,GAAI,EAAE,EAAE,OAAO,SAAS;MACrC,OAAO,SAAS;IAClB,CAAC;;IAED;IACA,MAAMC,UAAS,GAAKC,UAAU,IAAK;MACjC,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAIC,MAAM,CAACH,IAAI,CAACI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAClE,CAAC,EACD,GACF,CAAC,IAAIF,MAAM,CAACH,IAAI,CAACM,OAAO,CAAC,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAChD,CAAC;;IAED;IACA,MAAME,UAAS,GAAKR,UAAU,IAAK;MACjC,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAO,GAAGI,MAAM,CAACH,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,MAAM,CAC1DH,IAAI,CAACS,UAAU,CAAC,CAClB,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IACtB,CAAC;;IAED;IACA,MAAMK,eAAc,GAAKC,EAAE,IAAK;MAC9BtD,MAAM,CAAC+B,IAAI,CAAC,qBAAqBuB,EAAE,EAAE,CAAC;IACxC,CAAC;IAED7D,SAAS,CAAC,MAAM;MACdiB,cAAc,CAAC,CAAC;IAClB,CAAC,CAAC;IAEF,OAAO;MACLD,kBAAkB;MAClBP,cAAc;MACdC,UAAU;MACVC,WAAW;MACXE,eAAe;MACfE,eAAe;MACfH,OAAO;MACP6B,aAAa;MACbE,aAAa;MACbC,iBAAiB;MACjBI,UAAU;MACVS,UAAU;MACVG,eAAe;MACfvB,SAAS;MACTE,kBAAkB;MAClBC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}