{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, computed, onMounted, onUnmounted, reactive } from \"vue\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport wsManager from \"@/utils/websocket\";\nimport { ArrowLeft, Refresh, Location, Clock, User, InfoFilled, Lightning, Sunny } from \"@element-plus/icons-vue\";\n// 导入API\nimport seatApi from \"@/api/seat\";\nexport default {\n  name: \"SeatMap\",\n  setup() {\n    const route = useRoute();\n    const router = useRouter();\n    const loading = ref(true);\n    const room = ref(null);\n    const seats = ref([]);\n    const selectedDate = ref(formatDateForSelect(new Date()));\n    const seatDialogVisible = ref(false);\n    const selectedSeat = ref(null);\n\n    // 过滤条件\n    const filters = reactive({\n      powerOutlet: false,\n      windowSeat: false,\n      availableOnly: false\n    });\n\n    // 日期选项\n    const dateOptions = computed(() => {\n      const options = [];\n      const today = new Date();\n      for (let i = 0; i < 7; i++) {\n        const date = new Date();\n        date.setDate(today.getDate() + i);\n        options.push({\n          value: formatDateForSelect(date),\n          label: formatDateForDisplay(date)\n        });\n      }\n      return options;\n    });\n\n    // 可用座位数量\n    const availableSeats = computed(() => {\n      return seats.value.filter(seat => seat.status === \"available\").length;\n    });\n\n    // 过滤后的座位\n    const filteredSeats = computed(() => {\n      return seats.value.filter(seat => {\n        if (filters.powerOutlet && !seat.is_power_outlet) return false;\n        if (filters.windowSeat && !seat.is_window_seat) return false;\n        if (filters.availableOnly && seat.status !== \"available\") return false;\n        return true;\n      });\n    });\n\n    // 座位网格样式\n    const gridStyle = computed(() => {\n      if (!room.value) return {};\n\n      // 固定6x6网格\n      return {\n        gridTemplateRows: `repeat(6, 60px)`,\n        gridTemplateColumns: `repeat(6, 60px)`\n      };\n    });\n\n    // 加载自习室和座位信息\n    const loadRoomAndSeats = async () => {\n      try {\n        loading.value = true;\n        const roomId = parseInt(route.query.roomId);\n        if (!roomId) {\n          ElMessage.error(\"缺少自习室ID参数\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 从模拟数据中查找自习室\n        const roomData = mockRooms.find(r => r.id === roomId);\n        if (!roomData) {\n          ElMessage.error(\"未找到自习室信息\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 转换数据格式\n        room.value = {\n          ...roomData,\n          open_time: roomData.openTime,\n          close_time: roomData.closeTime,\n          available_seats: roomData.availableSeats\n        };\n\n        // 加载座位信息\n        await loadSeats();\n      } catch (error) {\n        ElMessage.error(\"加载自习室信息失败\");\n        router.push(\"/seat/rooms\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载座位信息\n    const loadSeats = async () => {\n      try {\n        loading.value = true;\n        const roomId = parseInt(route.query.roomId);\n        if (!roomId) return;\n\n        // 模拟API请求延迟\n        await new Promise(resolve => setTimeout(resolve, 500));\n\n        // 使用模拟数据生成座位\n        const seatsData = generateSeats(roomId);\n\n        // 转换数据格式\n        seats.value = seatsData.map(seat => ({\n          ...seat,\n          is_power_outlet: seat.isPowerOutlet,\n          is_window_seat: seat.isWindowSeat,\n          seat_number: seat.seatNumber\n        }));\n      } catch (error) {\n        ElMessage.error(\"加载座位信息失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 应用过滤器\n    const applyFilters = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 选择座位\n    const selectSeat = seat => {\n      selectedSeat.value = seat;\n      seatDialogVisible.value = true;\n    };\n\n    // 预约座位\n    const reserveSeat = () => {\n      if (!selectedSeat.value) return;\n      router.push({\n        path: \"/seat/reservation\",\n        query: {\n          seatId: selectedSeat.value.id,\n          date: selectedDate.value\n        }\n      });\n    };\n\n    // 获取座位类名\n    const getSeatClasses = seat => {\n      return {\n        \"seat-available\": seat.status === \"available\",\n        \"seat-occupied\": seat.status === \"occupied\",\n        \"seat-disabled\": seat.status === \"disabled\",\n        \"seat-power\": seat.is_power_outlet,\n        \"seat-window\": seat.is_window_seat\n      };\n    };\n\n    // 获取座位样式\n    const getSeatStyle = seat => {\n      return {\n        gridRow: `${seat.row} / span 1`,\n        gridColumn: `${seat.column} / span 1`\n      };\n    };\n\n    // 获取座位状态类型\n    const getSeatStatusType = status => {\n      switch (status) {\n        case \"available\":\n          return \"success\";\n        case \"occupied\":\n          return \"danger\";\n        case \"disabled\":\n          return \"info\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取座位状态文本\n    const getSeatStatusText = status => {\n      switch (status) {\n        case \"available\":\n          return \"可用\";\n        case \"occupied\":\n          return \"已占用\";\n        case \"disabled\":\n          return \"禁用\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 获取预约状态类型\n    const getReservationStatusType = status => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = status => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化时间\n    const formatTime = timeString => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 格式化日期时间\n    const formatDateTime = dateTimeString => {\n      if (!dateTimeString) return \"\";\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 格式化日期（用于选择器值）\n    function formatDateForSelect(date) {\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())}`;\n    }\n\n    // 格式化日期（用于显示）\n    function formatDateForDisplay(date) {\n      const today = new Date();\n      const tomorrow = new Date();\n      tomorrow.setDate(today.getDate() + 1);\n      if (date.toDateString() === today.toDateString()) {\n        return \"今天\";\n      } else if (date.toDateString() === tomorrow.toDateString()) {\n        return \"明天\";\n      } else {\n        const weekdays = [\"周日\", \"周一\", \"周二\", \"周三\", \"周四\", \"周五\", \"周六\"];\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${weekdays[date.getDay()]}`;\n      }\n    }\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // WebSocket连接管理\n    const connectWebSocket = () => {\n      const roomIdValue = parseInt(route.query.roomId);\n      if (!roomIdValue) return;\n      const wsUrl = `ws://localhost:8000/ws/seat/${roomIdValue}/`;\n      wsManager.connect(wsUrl, \"seatStatus\", {\n        onAuthenticated: () => {\n          console.log(\"WebSocket认证成功，订阅座位状态\");\n          wsManager.subscribeSeatStatus(\"seatStatus\", roomIdValue);\n        },\n        onMessage: data => {\n          console.log(\"收到WebSocket消息:\", data);\n        },\n        onClose: () => {\n          console.log(\"WebSocket连接已关闭\");\n        },\n        onError: error => {\n          console.error(\"WebSocket连接错误:\", error);\n        }\n      });\n    };\n    const disconnectWebSocket = () => {\n      wsManager.disconnect(\"seatStatus\");\n    };\n    onMounted(() => {\n      loadRoomAndSeats();\n      connectWebSocket();\n    });\n    onUnmounted(() => {\n      disconnectWebSocket();\n    });\n    return {\n      loading,\n      room,\n      seats,\n      selectedDate,\n      dateOptions,\n      filters,\n      seatDialogVisible,\n      selectedSeat,\n      availableSeats,\n      filteredSeats,\n      gridStyle,\n      loadSeats,\n      applyFilters,\n      selectSeat,\n      reserveSeat,\n      getSeatClasses,\n      getSeatStyle,\n      getSeatStatusType,\n      getSeatStatusText,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatTime,\n      formatDateTime,\n      ArrowLeft,\n      Refresh,\n      Location,\n      Clock,\n      User,\n      InfoFilled,\n      Lightning,\n      Sunny\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "reactive", "useRoute", "useRouter", "ElMessage", "wsManager", "ArrowLeft", "Refresh", "Location", "Clock", "User", "InfoFilled", "Lightning", "<PERSON>", "seatApi", "name", "setup", "route", "router", "loading", "room", "seats", "selectedDate", "formatDateForSelect", "Date", "seatDialogVisible", "selectedSeat", "filters", "powerOutlet", "windowSeat", "availableOnly", "dateOptions", "options", "today", "i", "date", "setDate", "getDate", "push", "value", "label", "formatDateForDisplay", "availableSeats", "filter", "seat", "status", "length", "filteredSeats", "is_power_outlet", "is_window_seat", "gridStyle", "gridTemplateRows", "gridTemplateColumns", "loadRoomAndSeats", "roomId", "parseInt", "query", "error", "roomData", "mockRooms", "find", "r", "id", "open_time", "openTime", "close_time", "closeTime", "available_seats", "loadSeats", "Promise", "resolve", "setTimeout", "seatsData", "generateSeats", "map", "isPowerOutlet", "isWindowSeat", "seat_number", "seatNumber", "applyFilters", "selectSeat", "reserveSeat", "path", "seatId", "getSeatClasses", "getSeatStyle", "gridRow", "row", "gridColumn", "column", "getSeatStatusType", "getSeatStatusText", "getReservationStatusType", "getReservationStatusText", "formatTime", "timeString", "substring", "formatDateTime", "dateTimeString", "getFullYear", "padZero", "getMonth", "getHours", "getMinutes", "tomorrow", "toDateString", "weekdays", "getDay", "num", "connectWebSocket", "roomIdValue", "wsUrl", "connect", "onAuthenticated", "console", "log", "subscribeSeatStatus", "onMessage", "data", "onClose", "onError", "disconnectWebSocket", "disconnect"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\seat\\SeatMap.vue"], "sourcesContent": ["<template>\n  <div class=\"seat-map\">\n    <div class=\"page-header\">\n      <div class=\"header-left\">\n        <el-button @click=\"$router.back()\" :icon=\"ArrowLeft\">返回</el-button>\n        <h2 v-if=\"room\">{{ room.name }} - 座位图</h2>\n      </div>\n\n      <div class=\"header-right\">\n        <el-select\n          v-model=\"selectedDate\"\n          placeholder=\"选择日期\"\n          @change=\"loadSeats\"\n        >\n          <el-option\n            v-for=\"date in dateOptions\"\n            :key=\"date.value\"\n            :label=\"date.label\"\n            :value=\"date.value\"\n          />\n        </el-select>\n\n        <el-button type=\"primary\" @click=\"loadSeats\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n      </div>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <template v-else>\n      <el-card class=\"room-info-card\" shadow=\"never\">\n        <div class=\"room-info\">\n          <div class=\"info-item\">\n            <el-icon><Location /></el-icon>\n            <span>位置: {{ room?.location }}</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><Clock /></el-icon>\n            <span>\n              开放时间: {{ formatTime(room?.open_time) }} -\n              {{ formatTime(room?.close_time) }}\n            </span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><User /></el-icon>\n            <span>容量: {{ room?.capacity }}座</span>\n          </div>\n\n          <div class=\"info-item\">\n            <el-icon><InfoFilled /></el-icon>\n            <span>可用座位: {{ availableSeats }}/{{ room?.capacity }}</span>\n          </div>\n        </div>\n\n        <div class=\"seat-legend\">\n          <div class=\"legend-item\">\n            <div class=\"seat-icon available\"></div>\n            <span>可用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon occupied\"></div>\n            <span>已占用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon disabled\"></div>\n            <span>禁用</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon power-outlet\"></div>\n            <span>电源</span>\n          </div>\n\n          <div class=\"legend-item\">\n            <div class=\"seat-icon window\"></div>\n            <span>靠窗</span>\n          </div>\n        </div>\n      </el-card>\n\n      <div class=\"map-container\">\n        <div class=\"seat-filter\">\n          <el-checkbox v-model=\"filters.powerOutlet\" @change=\"applyFilters\">\n            只看有电源的座位\n          </el-checkbox>\n\n          <el-checkbox v-model=\"filters.windowSeat\" @change=\"applyFilters\">\n            只看靠窗座位\n          </el-checkbox>\n\n          <el-checkbox v-model=\"filters.availableOnly\" @change=\"applyFilters\">\n            只看可用座位\n          </el-checkbox>\n        </div>\n\n        <div class=\"seat-grid\" :style=\"gridStyle\">\n          <div\n            v-for=\"seat in filteredSeats\"\n            :key=\"seat.id\"\n            class=\"seat\"\n            :class=\"getSeatClasses(seat)\"\n            :style=\"getSeatStyle(seat)\"\n            @click=\"selectSeat(seat)\"\n          >\n            <div class=\"seat-number\">{{ seat.seat_number }}</div>\n            <div class=\"seat-icons\">\n              <el-icon v-if=\"seat.is_power_outlet\" class=\"power-icon\"\n                ><Lightning\n              /></el-icon>\n              <el-icon v-if=\"seat.is_window_seat\" class=\"window-icon\"\n                ><Sunny\n              /></el-icon>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 座位详情对话框 -->\n      <el-dialog\n        v-model=\"seatDialogVisible\"\n        :title=\"`座位详情 - ${selectedSeat?.seat_number}`\"\n        width=\"500px\"\n      >\n        <div v-if=\"selectedSeat\" class=\"seat-detail\">\n          <el-descriptions :column=\"1\" border>\n            <el-descriptions-item label=\"座位编号\">\n              {{ selectedSeat.seat_number }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"位置\">\n              {{ `${selectedSeat.row}排${selectedSeat.column}列` }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"状态\">\n              <el-tag :type=\"getSeatStatusType(selectedSeat.status)\">\n                {{ getSeatStatusText(selectedSeat.status) }}\n              </el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"设施\">\n              <el-tag\n                v-if=\"selectedSeat.is_power_outlet\"\n                type=\"success\"\n                effect=\"plain\"\n              >\n                有电源\n              </el-tag>\n              <el-tag\n                v-if=\"selectedSeat.is_window_seat\"\n                type=\"success\"\n                effect=\"plain\"\n                >靠窗</el-tag\n              >\n              <span\n                v-if=\"\n                  !selectedSeat.is_power_outlet && !selectedSeat.is_window_seat\n                \"\n              >\n                无特殊设施\n              </span>\n            </el-descriptions-item>\n          </el-descriptions>\n\n          <div\n            v-if=\"selectedSeat.current_reservation\"\n            class=\"current-reservation\"\n          >\n            <h4>当前预约信息</h4>\n            <el-descriptions :column=\"1\" border>\n              <el-descriptions-item label=\"预约状态\">\n                <el-tag\n                  :type=\"\n                    getReservationStatusType(\n                      selectedSeat.current_reservation.status\n                    )\n                  \"\n                >\n                  {{\n                    getReservationStatusText(\n                      selectedSeat.current_reservation.status\n                    )\n                  }}\n                </el-tag>\n              </el-descriptions-item>\n              <el-descriptions-item label=\"开始时间\">\n                {{\n                  formatDateTime(selectedSeat.current_reservation.start_time)\n                }}\n              </el-descriptions-item>\n              <el-descriptions-item label=\"结束时间\">\n                {{ formatDateTime(selectedSeat.current_reservation.end_time) }}\n              </el-descriptions-item>\n            </el-descriptions>\n          </div>\n\n          <div v-if=\"selectedSeat.status === 'available'\" class=\"seat-actions\">\n            <el-button type=\"primary\" @click=\"reserveSeat\"\n              >预约此座位</el-button\n            >\n          </div>\n        </div>\n      </el-dialog>\n    </template>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, onUnmounted, reactive } from \"vue\";\nimport { useRoute, useRouter } from \"vue-router\";\nimport { ElMessage } from \"element-plus\";\nimport wsManager from \"@/utils/websocket\";\nimport {\n  ArrowLeft,\n  Refresh,\n  Location,\n  Clock,\n  User,\n  InfoFilled,\n  Lightning,\n  Sunny,\n} from \"@element-plus/icons-vue\";\n// 导入API\nimport seatApi from \"@/api/seat\";\n\nexport default {\n  name: \"SeatMap\",\n  setup() {\n    const route = useRoute();\n    const router = useRouter();\n\n    const loading = ref(true);\n    const room = ref(null);\n    const seats = ref([]);\n    const selectedDate = ref(formatDateForSelect(new Date()));\n    const seatDialogVisible = ref(false);\n    const selectedSeat = ref(null);\n\n    // 过滤条件\n    const filters = reactive({\n      powerOutlet: false,\n      windowSeat: false,\n      availableOnly: false,\n    });\n\n    // 日期选项\n    const dateOptions = computed(() => {\n      const options = [];\n      const today = new Date();\n\n      for (let i = 0; i < 7; i++) {\n        const date = new Date();\n        date.setDate(today.getDate() + i);\n\n        options.push({\n          value: formatDateForSelect(date),\n          label: formatDateForDisplay(date),\n        });\n      }\n\n      return options;\n    });\n\n    // 可用座位数量\n    const availableSeats = computed(() => {\n      return seats.value.filter((seat) => seat.status === \"available\").length;\n    });\n\n    // 过滤后的座位\n    const filteredSeats = computed(() => {\n      return seats.value.filter((seat) => {\n        if (filters.powerOutlet && !seat.is_power_outlet) return false;\n        if (filters.windowSeat && !seat.is_window_seat) return false;\n        if (filters.availableOnly && seat.status !== \"available\") return false;\n        return true;\n      });\n    });\n\n    // 座位网格样式\n    const gridStyle = computed(() => {\n      if (!room.value) return {};\n\n      // 固定6x6网格\n      return {\n        gridTemplateRows: `repeat(6, 60px)`,\n        gridTemplateColumns: `repeat(6, 60px)`,\n      };\n    });\n\n    // 加载自习室和座位信息\n    const loadRoomAndSeats = async () => {\n      try {\n        loading.value = true;\n\n        const roomId = parseInt(route.query.roomId);\n        if (!roomId) {\n          ElMessage.error(\"缺少自习室ID参数\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 从模拟数据中查找自习室\n        const roomData = mockRooms.find((r) => r.id === roomId);\n        if (!roomData) {\n          ElMessage.error(\"未找到自习室信息\");\n          router.push(\"/seat/rooms\");\n          return;\n        }\n\n        // 转换数据格式\n        room.value = {\n          ...roomData,\n          open_time: roomData.openTime,\n          close_time: roomData.closeTime,\n          available_seats: roomData.availableSeats,\n        };\n\n        // 加载座位信息\n        await loadSeats();\n      } catch (error) {\n        ElMessage.error(\"加载自习室信息失败\");\n        router.push(\"/seat/rooms\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载座位信息\n    const loadSeats = async () => {\n      try {\n        loading.value = true;\n\n        const roomId = parseInt(route.query.roomId);\n        if (!roomId) return;\n\n        // 模拟API请求延迟\n        await new Promise((resolve) => setTimeout(resolve, 500));\n\n        // 使用模拟数据生成座位\n        const seatsData = generateSeats(roomId);\n\n        // 转换数据格式\n        seats.value = seatsData.map((seat) => ({\n          ...seat,\n          is_power_outlet: seat.isPowerOutlet,\n          is_window_seat: seat.isWindowSeat,\n          seat_number: seat.seatNumber,\n        }));\n      } catch (error) {\n        ElMessage.error(\"加载座位信息失败\");\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 应用过滤器\n    const applyFilters = () => {\n      // 过滤逻辑已在计算属性中实现\n    };\n\n    // 选择座位\n    const selectSeat = (seat) => {\n      selectedSeat.value = seat;\n      seatDialogVisible.value = true;\n    };\n\n    // 预约座位\n    const reserveSeat = () => {\n      if (!selectedSeat.value) return;\n\n      router.push({\n        path: \"/seat/reservation\",\n        query: {\n          seatId: selectedSeat.value.id,\n          date: selectedDate.value,\n        },\n      });\n    };\n\n    // 获取座位类名\n    const getSeatClasses = (seat) => {\n      return {\n        \"seat-available\": seat.status === \"available\",\n        \"seat-occupied\": seat.status === \"occupied\",\n        \"seat-disabled\": seat.status === \"disabled\",\n        \"seat-power\": seat.is_power_outlet,\n        \"seat-window\": seat.is_window_seat,\n      };\n    };\n\n    // 获取座位样式\n    const getSeatStyle = (seat) => {\n      return {\n        gridRow: `${seat.row} / span 1`,\n        gridColumn: `${seat.column} / span 1`,\n      };\n    };\n\n    // 获取座位状态类型\n    const getSeatStatusType = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"success\";\n        case \"occupied\":\n          return \"danger\";\n        case \"disabled\":\n          return \"info\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取座位状态文本\n    const getSeatStatusText = (status) => {\n      switch (status) {\n        case \"available\":\n          return \"可用\";\n        case \"occupied\":\n          return \"已占用\";\n        case \"disabled\":\n          return \"禁用\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 获取预约状态类型\n    const getReservationStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getReservationStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知状态\";\n      }\n    };\n\n    // 格式化时间\n    const formatTime = (timeString) => {\n      if (!timeString) return \"\";\n\n      // 时间格式为 \"HH:MM:SS\"，只显示 \"HH:MM\"\n      return timeString.substring(0, 5);\n    };\n\n    // 格式化日期时间\n    const formatDateTime = (dateTimeString) => {\n      if (!dateTimeString) return \"\";\n\n      const date = new Date(dateTimeString);\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`;\n    };\n\n    // 格式化日期（用于选择器值）\n    function formatDateForSelect(date) {\n      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(\n        date.getDate()\n      )}`;\n    }\n\n    // 格式化日期（用于显示）\n    function formatDateForDisplay(date) {\n      const today = new Date();\n      const tomorrow = new Date();\n      tomorrow.setDate(today.getDate() + 1);\n\n      if (date.toDateString() === today.toDateString()) {\n        return \"今天\";\n      } else if (date.toDateString() === tomorrow.toDateString()) {\n        return \"明天\";\n      } else {\n        const weekdays = [\n          \"周日\",\n          \"周一\",\n          \"周二\",\n          \"周三\",\n          \"周四\",\n          \"周五\",\n          \"周六\",\n        ];\n        return `${date.getMonth() + 1}月${date.getDate()}日 ${\n          weekdays[date.getDay()]\n        }`;\n      }\n    }\n\n    // 补零\n    function padZero(num) {\n      return num < 10 ? `0${num}` : num;\n    }\n\n    // WebSocket连接管理\n    const connectWebSocket = () => {\n      const roomIdValue = parseInt(route.query.roomId);\n      if (!roomIdValue) return;\n\n      const wsUrl = `ws://localhost:8000/ws/seat/${roomIdValue}/`;\n      wsManager.connect(wsUrl, \"seatStatus\", {\n        onAuthenticated: () => {\n          console.log(\"WebSocket认证成功，订阅座位状态\");\n          wsManager.subscribeSeatStatus(\"seatStatus\", roomIdValue);\n        },\n        onMessage: (data) => {\n          console.log(\"收到WebSocket消息:\", data);\n        },\n        onClose: () => {\n          console.log(\"WebSocket连接已关闭\");\n        },\n        onError: (error) => {\n          console.error(\"WebSocket连接错误:\", error);\n        },\n      });\n    };\n\n    const disconnectWebSocket = () => {\n      wsManager.disconnect(\"seatStatus\");\n    };\n\n    onMounted(() => {\n      loadRoomAndSeats();\n      connectWebSocket();\n    });\n\n    onUnmounted(() => {\n      disconnectWebSocket();\n    });\n\n    return {\n      loading,\n      room,\n      seats,\n      selectedDate,\n      dateOptions,\n      filters,\n      seatDialogVisible,\n      selectedSeat,\n      availableSeats,\n      filteredSeats,\n      gridStyle,\n      loadSeats,\n      applyFilters,\n      selectSeat,\n      reserveSeat,\n      getSeatClasses,\n      getSeatStyle,\n      getSeatStatusType,\n      getSeatStatusText,\n      getReservationStatusType,\n      getReservationStatusText,\n      formatTime,\n      formatDateTime,\n      ArrowLeft,\n      Refresh,\n      Location,\n      Clock,\n      User,\n      InfoFilled,\n      Lightning,\n      Sunny,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.seat-map {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n\n  .header-left {\n    display: flex;\n    align-items: center;\n    gap: 15px;\n\n    h2 {\n      margin: 0;\n    }\n  }\n\n  .header-right {\n    display: flex;\n    gap: 10px;\n  }\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.room-info-card {\n  margin-bottom: 20px;\n\n  .room-info {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 20px;\n    margin-bottom: 15px;\n\n    .info-item {\n      display: flex;\n      align-items: center;\n\n      .el-icon {\n        margin-right: 8px;\n        color: #909399;\n      }\n    }\n  }\n\n  .seat-legend {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 15px;\n\n    .legend-item {\n      display: flex;\n      align-items: center;\n\n      .seat-icon {\n        width: 20px;\n        height: 20px;\n        border-radius: 4px;\n        margin-right: 5px;\n\n        &.available {\n          background-color: #67c23a;\n        }\n\n        &.occupied {\n          background-color: #f56c6c;\n        }\n\n        &.disabled {\n          background-color: #909399;\n        }\n\n        &.power-outlet {\n          background-color: #fff;\n          border: 1px solid #67c23a;\n          position: relative;\n\n          &::after {\n            content: \"⚡\";\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            font-size: 12px;\n          }\n        }\n\n        &.window {\n          background-color: #fff;\n          border: 1px solid #409eff;\n          position: relative;\n\n          &::after {\n            content: \"☀\";\n            position: absolute;\n            top: 50%;\n            left: 50%;\n            transform: translate(-50%, -50%);\n            font-size: 12px;\n          }\n        }\n      }\n    }\n  }\n}\n\n.map-container {\n  background-color: #fff;\n  border-radius: 4px;\n  padding: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n\n  .seat-filter {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 20px;\n  }\n\n  .seat-grid {\n    display: grid;\n    gap: 10px;\n    justify-content: center;\n    margin-top: 20px;\n\n    .seat {\n      width: 50px;\n      height: 50px;\n      border-radius: 4px;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      align-items: center;\n      cursor: pointer;\n      transition: transform 0.2s;\n      position: relative;\n\n      &:hover {\n        transform: scale(1.1);\n        z-index: 1;\n      }\n\n      &.seat-available {\n        background-color: #67c23a;\n        color: #fff;\n      }\n\n      &.seat-occupied {\n        background-color: #f56c6c;\n        color: #fff;\n      }\n\n      &.seat-disabled {\n        background-color: #909399;\n        color: #fff;\n        cursor: not-allowed;\n\n        &:hover {\n          transform: none;\n        }\n      }\n\n      .seat-number {\n        font-weight: bold;\n        font-size: 14px;\n      }\n\n      .seat-icons {\n        display: flex;\n        gap: 2px;\n        margin-top: 2px;\n\n        .power-icon,\n        .window-icon {\n          font-size: 12px;\n        }\n      }\n    }\n  }\n}\n\n.seat-detail {\n  .current-reservation {\n    margin-top: 20px;\n  }\n\n  .seat-actions {\n    margin-top: 20px;\n    text-align: center;\n  }\n}\n</style>\n"], "mappings": ";;;;;AAoNA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAO,QAAS,KAAK;AACrE,SAASC,QAAQ,EAAEC,SAAQ,QAAS,YAAY;AAChD,SAASC,SAAQ,QAAS,cAAc;AACxC,OAAOC,SAAQ,MAAO,mBAAmB;AACzC,SACEC,SAAS,EACTC,OAAO,EACPC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,SAAS,EACTC,KAAK,QACA,yBAAyB;AAChC;AACA,OAAOC,OAAM,MAAO,YAAY;AAEhC,eAAe;EACbC,IAAI,EAAE,SAAS;EACfC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIf,QAAQ,CAAC,CAAC;IACxB,MAAMgB,MAAK,GAAIf,SAAS,CAAC,CAAC;IAE1B,MAAMgB,OAAM,GAAItB,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMuB,IAAG,GAAIvB,GAAG,CAAC,IAAI,CAAC;IACtB,MAAMwB,KAAI,GAAIxB,GAAG,CAAC,EAAE,CAAC;IACrB,MAAMyB,YAAW,GAAIzB,GAAG,CAAC0B,mBAAmB,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC;IACzD,MAAMC,iBAAgB,GAAI5B,GAAG,CAAC,KAAK,CAAC;IACpC,MAAM6B,YAAW,GAAI7B,GAAG,CAAC,IAAI,CAAC;;IAE9B;IACA,MAAM8B,OAAM,GAAI1B,QAAQ,CAAC;MACvB2B,WAAW,EAAE,KAAK;MAClBC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE;IACjB,CAAC,CAAC;;IAEF;IACA,MAAMC,WAAU,GAAIjC,QAAQ,CAAC,MAAM;MACjC,MAAMkC,OAAM,GAAI,EAAE;MAClB,MAAMC,KAAI,GAAI,IAAIT,IAAI,CAAC,CAAC;MAExB,KAAK,IAAIU,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,IAAG,GAAI,IAAIX,IAAI,CAAC,CAAC;QACvBW,IAAI,CAACC,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,IAAIH,CAAC,CAAC;QAEjCF,OAAO,CAACM,IAAI,CAAC;UACXC,KAAK,EAAEhB,mBAAmB,CAACY,IAAI,CAAC;UAChCK,KAAK,EAAEC,oBAAoB,CAACN,IAAI;QAClC,CAAC,CAAC;MACJ;MAEA,OAAOH,OAAO;IAChB,CAAC,CAAC;;IAEF;IACA,MAAMU,cAAa,GAAI5C,QAAQ,CAAC,MAAM;MACpC,OAAOuB,KAAK,CAACkB,KAAK,CAACI,MAAM,CAAEC,IAAI,IAAKA,IAAI,CAACC,MAAK,KAAM,WAAW,CAAC,CAACC,MAAM;IACzE,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAY,GAAIjD,QAAQ,CAAC,MAAM;MACnC,OAAOuB,KAAK,CAACkB,KAAK,CAACI,MAAM,CAAEC,IAAI,IAAK;QAClC,IAAIjB,OAAO,CAACC,WAAU,IAAK,CAACgB,IAAI,CAACI,eAAe,EAAE,OAAO,KAAK;QAC9D,IAAIrB,OAAO,CAACE,UAAS,IAAK,CAACe,IAAI,CAACK,cAAc,EAAE,OAAO,KAAK;QAC5D,IAAItB,OAAO,CAACG,aAAY,IAAKc,IAAI,CAACC,MAAK,KAAM,WAAW,EAAE,OAAO,KAAK;QACtE,OAAO,IAAI;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAMK,SAAQ,GAAIpD,QAAQ,CAAC,MAAM;MAC/B,IAAI,CAACsB,IAAI,CAACmB,KAAK,EAAE,OAAO,CAAC,CAAC;;MAE1B;MACA,OAAO;QACLY,gBAAgB,EAAE,iBAAiB;QACnCC,mBAAmB,EAAE;MACvB,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMC,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFlC,OAAO,CAACoB,KAAI,GAAI,IAAI;QAEpB,MAAMe,MAAK,GAAIC,QAAQ,CAACtC,KAAK,CAACuC,KAAK,CAACF,MAAM,CAAC;QAC3C,IAAI,CAACA,MAAM,EAAE;UACXlD,SAAS,CAACqD,KAAK,CAAC,WAAW,CAAC;UAC5BvC,MAAM,CAACoB,IAAI,CAAC,aAAa,CAAC;UAC1B;QACF;;QAEA;QACA,MAAMoB,QAAO,GAAIC,SAAS,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAC,KAAMR,MAAM,CAAC;QACvD,IAAI,CAACI,QAAQ,EAAE;UACbtD,SAAS,CAACqD,KAAK,CAAC,UAAU,CAAC;UAC3BvC,MAAM,CAACoB,IAAI,CAAC,aAAa,CAAC;UAC1B;QACF;;QAEA;QACAlB,IAAI,CAACmB,KAAI,GAAI;UACX,GAAGmB,QAAQ;UACXK,SAAS,EAAEL,QAAQ,CAACM,QAAQ;UAC5BC,UAAU,EAAEP,QAAQ,CAACQ,SAAS;UAC9BC,eAAe,EAAET,QAAQ,CAAChB;QAC5B,CAAC;;QAED;QACA,MAAM0B,SAAS,CAAC,CAAC;MACnB,EAAE,OAAOX,KAAK,EAAE;QACdrD,SAAS,CAACqD,KAAK,CAAC,WAAW,CAAC;QAC5BvC,MAAM,CAACoB,IAAI,CAAC,aAAa,CAAC;MAC5B,UAAU;QACRnB,OAAO,CAACoB,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAM6B,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFjD,OAAO,CAACoB,KAAI,GAAI,IAAI;QAEpB,MAAMe,MAAK,GAAIC,QAAQ,CAACtC,KAAK,CAACuC,KAAK,CAACF,MAAM,CAAC;QAC3C,IAAI,CAACA,MAAM,EAAE;;QAEb;QACA,MAAM,IAAIe,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;QAExD;QACA,MAAME,SAAQ,GAAIC,aAAa,CAACnB,MAAM,CAAC;;QAEvC;QACAjC,KAAK,CAACkB,KAAI,GAAIiC,SAAS,CAACE,GAAG,CAAE9B,IAAI,KAAM;UACrC,GAAGA,IAAI;UACPI,eAAe,EAAEJ,IAAI,CAAC+B,aAAa;UACnC1B,cAAc,EAAEL,IAAI,CAACgC,YAAY;UACjCC,WAAW,EAAEjC,IAAI,CAACkC;QACpB,CAAC,CAAC,CAAC;MACL,EAAE,OAAOrB,KAAK,EAAE;QACdrD,SAAS,CAACqD,KAAK,CAAC,UAAU,CAAC;MAC7B,UAAU;QACRtC,OAAO,CAACoB,KAAI,GAAI,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMwC,YAAW,GAAIA,CAAA,KAAM;MACzB;IAAA,CACD;;IAED;IACA,MAAMC,UAAS,GAAKpC,IAAI,IAAK;MAC3BlB,YAAY,CAACa,KAAI,GAAIK,IAAI;MACzBnB,iBAAiB,CAACc,KAAI,GAAI,IAAI;IAChC,CAAC;;IAED;IACA,MAAM0C,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI,CAACvD,YAAY,CAACa,KAAK,EAAE;MAEzBrB,MAAM,CAACoB,IAAI,CAAC;QACV4C,IAAI,EAAE,mBAAmB;QACzB1B,KAAK,EAAE;UACL2B,MAAM,EAAEzD,YAAY,CAACa,KAAK,CAACuB,EAAE;UAC7B3B,IAAI,EAAEb,YAAY,CAACiB;QACrB;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAM6C,cAAa,GAAKxC,IAAI,IAAK;MAC/B,OAAO;QACL,gBAAgB,EAAEA,IAAI,CAACC,MAAK,KAAM,WAAW;QAC7C,eAAe,EAAED,IAAI,CAACC,MAAK,KAAM,UAAU;QAC3C,eAAe,EAAED,IAAI,CAACC,MAAK,KAAM,UAAU;QAC3C,YAAY,EAAED,IAAI,CAACI,eAAe;QAClC,aAAa,EAAEJ,IAAI,CAACK;MACtB,CAAC;IACH,CAAC;;IAED;IACA,MAAMoC,YAAW,GAAKzC,IAAI,IAAK;MAC7B,OAAO;QACL0C,OAAO,EAAE,GAAG1C,IAAI,CAAC2C,GAAG,WAAW;QAC/BC,UAAU,EAAE,GAAG5C,IAAI,CAAC6C,MAAM;MAC5B,CAAC;IACH,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAK7C,MAAM,IAAK;MACpC,QAAQA,MAAM;QACZ,KAAK,WAAW;UACd,OAAO,SAAS;QAClB,KAAK,UAAU;UACb,OAAO,QAAQ;QACjB,KAAK,UAAU;UACb,OAAO,MAAM;QACf;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAM8C,iBAAgB,GAAK9C,MAAM,IAAK;MACpC,QAAQA,MAAM;QACZ,KAAK,WAAW;UACd,OAAO,IAAI;QACb,KAAK,UAAU;UACb,OAAO,KAAK;QACd,KAAK,UAAU;UACb,OAAO,IAAI;QACb;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAM+C,wBAAuB,GAAK/C,MAAM,IAAK;MAC3C,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,SAAS;QAClB,KAAK,YAAY;UACf,OAAO,SAAS;QAClB,KAAK,WAAW;UACd,OAAO,MAAM;QACf,KAAK,WAAW;UACd,OAAO,QAAQ;QACjB,KAAK,SAAS;UACZ,OAAO,QAAQ;QACjB;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMgD,wBAAuB,GAAKhD,MAAM,IAAK;MAC3C,QAAQA,MAAM;QACZ,KAAK,SAAS;UACZ,OAAO,KAAK;QACd,KAAK,YAAY;UACf,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,WAAW;UACd,OAAO,KAAK;QACd,KAAK,SAAS;UACZ,OAAO,KAAK;QACd;UACE,OAAO,MAAM;MACjB;IACF,CAAC;;IAED;IACA,MAAMiD,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;;MAE1B;MACA,OAAOA,UAAU,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC;;IAED;IACA,MAAMC,cAAa,GAAKC,cAAc,IAAK;MACzC,IAAI,CAACA,cAAc,EAAE,OAAO,EAAE;MAE9B,MAAM/D,IAAG,GAAI,IAAIX,IAAI,CAAC0E,cAAc,CAAC;MACrC,OAAO,GAAG/D,IAAI,CAACgE,WAAW,CAAC,CAAC,IAAIC,OAAO,CAACjE,IAAI,CAACkE,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAID,OAAO,CACrEjE,IAAI,CAACE,OAAO,CAAC,CACf,CAAC,IAAI+D,OAAO,CAACjE,IAAI,CAACmE,QAAQ,CAAC,CAAC,CAAC,IAAIF,OAAO,CAACjE,IAAI,CAACoE,UAAU,CAAC,CAAC,CAAC,EAAE;IAC/D,CAAC;;IAED;IACA,SAAShF,mBAAmBA,CAACY,IAAI,EAAE;MACjC,OAAO,GAAGA,IAAI,CAACgE,WAAW,CAAC,CAAC,IAAIC,OAAO,CAACjE,IAAI,CAACkE,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAID,OAAO,CACrEjE,IAAI,CAACE,OAAO,CAAC,CACf,CAAC,EAAE;IACL;;IAEA;IACA,SAASI,oBAAoBA,CAACN,IAAI,EAAE;MAClC,MAAMF,KAAI,GAAI,IAAIT,IAAI,CAAC,CAAC;MACxB,MAAMgF,QAAO,GAAI,IAAIhF,IAAI,CAAC,CAAC;MAC3BgF,QAAQ,CAACpE,OAAO,CAACH,KAAK,CAACI,OAAO,CAAC,IAAI,CAAC,CAAC;MAErC,IAAIF,IAAI,CAACsE,YAAY,CAAC,MAAMxE,KAAK,CAACwE,YAAY,CAAC,CAAC,EAAE;QAChD,OAAO,IAAI;MACb,OAAO,IAAItE,IAAI,CAACsE,YAAY,CAAC,MAAMD,QAAQ,CAACC,YAAY,CAAC,CAAC,EAAE;QAC1D,OAAO,IAAI;MACb,OAAO;QACL,MAAMC,QAAO,GAAI,CACf,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;QACD,OAAO,GAAGvE,IAAI,CAACkE,QAAQ,CAAC,IAAI,CAAC,IAAIlE,IAAI,CAACE,OAAO,CAAC,CAAC,KAC7CqE,QAAQ,CAACvE,IAAI,CAACwE,MAAM,CAAC,CAAC,GACtB;MACJ;IACF;;IAEA;IACA,SAASP,OAAOA,CAACQ,GAAG,EAAE;MACpB,OAAOA,GAAE,GAAI,EAAC,GAAI,IAAIA,GAAG,EAAC,GAAIA,GAAG;IACnC;;IAEA;IACA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B,MAAMC,WAAU,GAAIvD,QAAQ,CAACtC,KAAK,CAACuC,KAAK,CAACF,MAAM,CAAC;MAChD,IAAI,CAACwD,WAAW,EAAE;MAElB,MAAMC,KAAI,GAAI,+BAA+BD,WAAW,GAAG;MAC3DzG,SAAS,CAAC2G,OAAO,CAACD,KAAK,EAAE,YAAY,EAAE;QACrCE,eAAe,EAAEA,CAAA,KAAM;UACrBC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;UACnC9G,SAAS,CAAC+G,mBAAmB,CAAC,YAAY,EAAEN,WAAW,CAAC;QAC1D,CAAC;QACDO,SAAS,EAAGC,IAAI,IAAK;UACnBJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEG,IAAI,CAAC;QACrC,CAAC;QACDC,OAAO,EAAEA,CAAA,KAAM;UACbL,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;QAC/B,CAAC;QACDK,OAAO,EAAG/D,KAAK,IAAK;UAClByD,OAAO,CAACzD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACxC;MACF,CAAC,CAAC;IACJ,CAAC;IAED,MAAMgE,mBAAkB,GAAIA,CAAA,KAAM;MAChCpH,SAAS,CAACqH,UAAU,CAAC,YAAY,CAAC;IACpC,CAAC;IAED3H,SAAS,CAAC,MAAM;MACdsD,gBAAgB,CAAC,CAAC;MAClBwD,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC;IAEF7G,WAAW,CAAC,MAAM;MAChByH,mBAAmB,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF,OAAO;MACLtG,OAAO;MACPC,IAAI;MACJC,KAAK;MACLC,YAAY;MACZS,WAAW;MACXJ,OAAO;MACPF,iBAAiB;MACjBC,YAAY;MACZgB,cAAc;MACdK,aAAa;MACbG,SAAS;MACTkB,SAAS;MACTW,YAAY;MACZC,UAAU;MACVC,WAAW;MACXG,cAAc;MACdC,YAAY;MACZK,iBAAiB;MACjBC,iBAAiB;MACjBC,wBAAwB;MACxBC,wBAAwB;MACxBC,UAAU;MACVG,cAAc;MACd3F,SAAS;MACTC,OAAO;MACPC,QAAQ;MACRC,KAAK;MACLC,IAAI;MACJC,UAAU;MACVC,SAAS;MACTC;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}