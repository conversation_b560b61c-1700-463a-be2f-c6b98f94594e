from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.seat.models import Room, Seat
from apps.admin_management.models import Admin, SystemConfig
from utils.crypto import SM3Hasher, SM4Crypto
import binascii
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '初始化真实的系统数据，替换模拟数据'

    def add_arguments(self, parser):
        parser.add_argument('--clear-mock', action='store_true', help='清除所有模拟数据')
        parser.add_argument('--create-rooms', action='store_true', help='创建自习室数据')
        parser.add_argument('--create-seats', action='store_true', help='创建座位数据')
        parser.add_argument('--create-admin', action='store_true', help='创建管理员账户')
        parser.add_argument('--admin-username', type=str, default='admin', help='管理员用户名')
        parser.add_argument('--admin-password', type=str, default='admin123', help='管理员密码')

    def handle(self, *args, **options):
        if options['clear_mock']:
            self.clear_mock_data()
        
        if options['create_rooms']:
            self.create_rooms()
        
        if options['create_seats']:
            self.create_seats()
        
        if options['create_admin']:
            self.create_admin_account(
                options['admin_username'], 
                options['admin_password']
            )

    def clear_mock_data(self):
        """清除模拟数据"""
        self.stdout.write('开始清除模拟数据...')
        
        # 清除用户相关的模拟数据（保留真实注册的用户）
        from apps.authentication.models import User
        mock_users = User.objects.filter(student_id_hash__startswith='2023')
        mock_count = mock_users.count()
        if mock_count > 0:
            mock_users.delete()
            self.stdout.write(f'已清除 {mock_count} 个模拟用户')
        
        # 清除模拟日志数据
        from apps.log.models import SystemLog, UserActionLog, SecurityLog, ApiRequestLog
        
        # 保留最近的系统日志，删除明显的模拟数据
        mock_system_logs = SystemLog.objects.filter(description__contains='模拟')
        mock_count = mock_system_logs.count()
        if mock_count > 0:
            mock_system_logs.delete()
            self.stdout.write(f'已清除 {mock_count} 条模拟系统日志')
        
        # 清除用户操作日志中的模拟数据
        mock_user_logs = UserActionLog.objects.filter(description__contains='模拟')
        mock_count = mock_user_logs.count()
        if mock_count > 0:
            mock_user_logs.delete()
            self.stdout.write(f'已清除 {mock_count} 条模拟用户操作日志')
        
        self.stdout.write(self.style.SUCCESS('模拟数据清除完成'))

    def create_rooms(self):
        """创建真实的自习室数据"""
        self.stdout.write('开始创建自习室数据...')
        
        # 图书馆1-6楼，每层东区西区各一个自习室
        floors = [1, 2, 3, 4, 5, 6]
        areas = ['东区', '西区']
        
        created_count = 0
        
        for floor in floors:
            for area in areas:
                room_name = f'{floor}楼{area}自习室'
                location = f'图书馆{floor}楼{area}'
                
                # 检查是否已存在
                if Room.objects.filter(name=room_name).exists():
                    self.stdout.write(f'自习室 {room_name} 已存在，跳过创建')
                    continue
                
                # 创建自习室
                room = Room.objects.create(
                    name=room_name,
                    location=location,
                    floor=floor,
                    capacity=36,  # 6x6座位布局
                    open_time='08:00:00',
                    close_time='22:00:00',
                    status='open',
                    description=f'图书馆{floor}楼{area}自习室，提供安静的学习环境'
                )
                
                created_count += 1
                self.stdout.write(f'已创建自习室: {room_name}')
        
        self.stdout.write(self.style.SUCCESS(f'自习室创建完成，共创建 {created_count} 个自习室'))

    def create_seats(self):
        """为所有自习室创建座位"""
        self.stdout.write('开始创建座位数据...')
        
        rooms = Room.objects.all()
        total_created = 0
        
        for room in rooms:
            # 检查是否已有座位
            if Seat.objects.filter(room=room).exists():
                self.stdout.write(f'自习室 {room.name} 已有座位，跳过创建')
                continue
            
            created_count = 0
            
            # 创建6x6座位布局
            for row in range(1, 7):
                for col in range(1, 7):
                    # 靠边的座位：第1行、第6行、第1列、第6列
                    is_edge_seat = row == 1 or row == 6 or col == 1 or col == 6
                    
                    # 每隔一位有电源插座
                    has_power_outlet = is_edge_seat and (row + col) % 2 == 0
                    
                    # 靠窗的座位：第1行和第6行
                    is_window_seat = row == 1 or row == 6
                    
                    seat = Seat.objects.create(
                        room=room,
                        seat_number=f'{row}-{col}',
                        row=row,
                        column=col,
                        status='available',
                        has_power_outlet=has_power_outlet,
                        is_window_seat=is_window_seat
                    )
                    
                    created_count += 1
            
            total_created += created_count
            self.stdout.write(f'为自习室 {room.name} 创建了 {created_count} 个座位')
        
        self.stdout.write(self.style.SUCCESS(f'座位创建完成，共创建 {total_created} 个座位'))

    def create_admin_account(self, username, password):
        """创建管理员账户"""
        self.stdout.write(f'开始创建管理员账户: {username}')
        
        # 检查是否已存在
        if Admin.objects.filter(username=username).exists():
            self.stdout.write(f'管理员 {username} 已存在')
            return
        
        # 生成SM4密钥
        sm4_key = SM4Crypto.generate_key()
        
        # 哈希密码
        password_hash_result = SM3Hasher.hash_with_salt(password)
        
        # 创建管理员
        admin = Admin.objects.create(
            username=username,
            password=password_hash_result['hash'],
            salt=password_hash_result['salt'],
            iterations=password_hash_result['iterations'],
            role='super_admin',
            email=binascii.unhexlify(SM4Crypto.encrypt(sm4_key, '<EMAIL>')),
            phone=binascii.unhexlify(SM4Crypto.encrypt(sm4_key, '***********')),
            status='active'
        )
        
        self.stdout.write(self.style.SUCCESS(f'管理员账户 {username} 创建成功'))
        self.stdout.write(f'用户名: {username}')
        self.stdout.write(f'密码: {password}')
        self.stdout.write(f'角色: 超级管理员')
