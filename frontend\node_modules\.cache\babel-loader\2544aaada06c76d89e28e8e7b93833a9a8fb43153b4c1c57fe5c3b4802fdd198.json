{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"statistic-suffix\"\n};\nconst _hoisted_4 = {\n  class: \"quick-actions\"\n};\nconst _hoisted_5 = {\n  class: \"card-header\"\n};\nconst _hoisted_6 = {\n  key: 0\n};\nconst _hoisted_7 = {\n  class: \"room-status-list\"\n};\nconst _hoisted_8 = [\"onClick\"];\nconst _hoisted_9 = {\n  class: \"room-info\"\n};\nconst _hoisted_10 = {\n  class: \"room-name\"\n};\nconst _hoisted_11 = {\n  class: \"room-capacity\"\n};\nconst _hoisted_12 = {\n  class: \"available-seats\"\n};\nconst _hoisted_13 = {\n  class: \"announcement-list\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_statistic = _resolveComponent(\"el-statistic\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_row, {\n    gutter: 20\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 16\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"welcome-card\"\n      }, {\n        header: _withCtx(() => _cache[5] || (_cache[5] = [_createElementVNode(\"div\", {\n          class: \"card-header\"\n        }, [_createElementVNode(\"h2\", null, \"欢迎使用基于国密算法的图书馆自习室座位管理系统\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_row, {\n          gutter: 20\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_col, {\n            span: 8\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_statistic, {\n              title: \"当前可用座位\",\n              value: $setup.availableSeats\n            }, {\n              suffix: _withCtx(() => [_createElementVNode(\"span\", _hoisted_3, \"/ \" + _toDisplayString($setup.totalSeats), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"value\"])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 8\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_statistic, {\n              title: \"我的预约\",\n              value: $setup.myReservations.length\n            }, {\n              suffix: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"span\", {\n                class: \"statistic-suffix\"\n              }, \"个\", -1 /* HOISTED */)])),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"value\"])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 8\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_statistic, {\n              title: \"我的信誉分\",\n              value: $setup.creditScore\n            }, {\n              suffix: _withCtx(() => [_createVNode(_component_el_tag, {\n                type: $setup.creditScoreType,\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.creditScoreText), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"type\"])]),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"value\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"div\", _hoisted_4, [_cache[11] || (_cache[11] = _createElementVNode(\"h3\", null, \"快捷操作\", -1 /* HOISTED */)), _createVNode(_component_el_row, {\n          gutter: 20\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_button, {\n              type: \"primary\",\n              icon: \"Plus\",\n              onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/seat/rooms')),\n              class: \"action-button\"\n            }, {\n              default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\" 预约座位 \")])),\n              _: 1 /* STABLE */,\n              __: [7]\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_button, {\n              type: \"success\",\n              icon: \"Location\",\n              onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/seat/checkin')),\n              class: \"action-button\"\n            }, {\n              default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\" 签到签退 \")])),\n              _: 1 /* STABLE */,\n              __: [8]\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_button, {\n              type: \"info\",\n              icon: \"List\",\n              onClick: _cache[2] || (_cache[2] = $event => _ctx.$router.push('/user/reservations')),\n              class: \"action-button\"\n            }, {\n              default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\" 我的预约 \")])),\n              _: 1 /* STABLE */,\n              __: [9]\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_col, {\n            span: 6\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_button, {\n              type: \"warning\",\n              icon: \"View\",\n              onClick: _cache[3] || (_cache[3] = $event => _ctx.$router.push('/seat/map')),\n              class: \"action-button\"\n            }, {\n              default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\" 座位地图 \")])),\n              _: 1 /* STABLE */,\n              __: [10]\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })])])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 最近预约 \"), _createVNode(_component_el_card, {\n        class: \"recent-reservations-card\"\n      }, {\n        header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_cache[13] || (_cache[13] = _createElementVNode(\"h3\", null, \"我的最近预约\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n          type: \"primary\",\n          link: \"\",\n          onClick: _cache[4] || (_cache[4] = $event => _ctx.$router.push('/user/reservations'))\n        }, {\n          default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\" 查看全部 \")])),\n          _: 1 /* STABLE */,\n          __: [12]\n        })])]),\n        default: _withCtx(() => [$setup.myReservations.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_el_table, {\n          data: $setup.myReservations.slice(0, 3),\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_table_column, {\n            prop: \"roomName\",\n            label: \"自习室\",\n            width: \"180\"\n          }), _createVNode(_component_el_table_column, {\n            prop: \"seatNumber\",\n            label: \"座位号\",\n            width: \"100\"\n          }), _createVNode(_component_el_table_column, {\n            label: \"时间\"\n          }, {\n            default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.startTime)) + \" \" + _toDisplayString($setup.formatTime(scope.row.startTime)) + \" - \" + _toDisplayString($setup.formatTime(scope.row.endTime)), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_table_column, {\n            label: \"状态\",\n            width: \"100\"\n          }, {\n            default: _withCtx(scope => [_createVNode(_component_el_tag, {\n              type: $setup.getStatusType(scope.row.status)\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(scope.row.status)), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_table_column, {\n            label: \"操作\",\n            width: \"120\"\n          }, {\n            default: _withCtx(scope => [_createVNode(_component_el_button, {\n              link: \"\",\n              type: \"primary\",\n              onClick: $event => $setup.viewReservation(scope.row.id)\n            }, {\n              default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\" 查看 \")])),\n              _: 2 /* DYNAMIC */,\n              __: [14]\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"data\"])])) : (_openBlock(), _createBlock(_component_el_empty, {\n          key: 1,\n          description: \"暂无预约记录\"\n        }))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 8\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 自习室状态 \"), _createVNode(_component_el_card, {\n        class: \"room-status-card\"\n      }, {\n        header: _withCtx(() => _cache[15] || (_cache[15] = [_createElementVNode(\"div\", {\n          class: \"card-header\"\n        }, [_createElementVNode(\"h3\", null, \"自习室状态\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_scrollbar, {\n          height: \"200px\"\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.rooms.slice(0, 6), room => {\n            return _openBlock(), _createElementBlock(\"div\", {\n              key: room.id,\n              class: \"room-status-item\",\n              onClick: $event => _ctx.$router.push(`/seat/map?roomId=${room.id}`)\n            }, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString(room.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_11, [_cache[16] || (_cache[16] = _createTextVNode(\" 可用: \")), _createElementVNode(\"span\", _hoisted_12, _toDisplayString(room.availableSeats), 1 /* TEXT */), _createTextVNode(\" / \" + _toDisplayString(room.capacity), 1 /* TEXT */)])]), _createVNode(_component_el_progress, {\n              percentage: Math.round(room.availableSeats / room.capacity * 100),\n              status: $setup.getProgressStatus(room.availableSeats, room.capacity)\n            }, null, 8 /* PROPS */, [\"percentage\", \"status\"])], 8 /* PROPS */, _hoisted_8);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        })])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 系统公告 \"), _createVNode(_component_el_card, {\n        class: \"announcement-card\"\n      }, {\n        header: _withCtx(() => _cache[17] || (_cache[17] = [_createElementVNode(\"div\", {\n          class: \"card-header\"\n        }, [_createElementVNode(\"h3\", null, \"系统公告\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n          class: \"announcement-item\"\n        }, [_createElementVNode(\"div\", {\n          class: \"announcement-title\"\n        }, \"系统升级通知\"), _createElementVNode(\"div\", {\n          class: \"announcement-time\"\n        }, \"2023-05-15\"), _createElementVNode(\"div\", {\n          class: \"announcement-content\"\n        }, \" 系统将于本周六凌晨2:00-4:00进行升级维护，期间系统将暂停服务，请提前做好安排。 \")], -1 /* HOISTED */)), _createVNode(_component_el_divider), _cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n          class: \"announcement-item\"\n        }, [_createElementVNode(\"div\", {\n          class: \"announcement-title\"\n        }, \"图书馆开放时间调整\"), _createElementVNode(\"div\", {\n          class: \"announcement-time\"\n        }, \"2023-05-10\"), _createElementVNode(\"div\", {\n          class: \"announcement-content\"\n        }, \" 自5月15日起，图书馆开放时间调整为8:00-22:00，请知悉。 \")], -1 /* HOISTED */))])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_row", "gutter", "default", "_withCtx", "_component_el_col", "span", "_component_el_card", "header", "_cache", "_createElementVNode", "_hoisted_2", "_component_el_statistic", "title", "value", "$setup", "availableSeats", "suffix", "_hoisted_3", "_toDisplayString", "totalSeats", "_", "myReservations", "length", "creditScore", "_component_el_tag", "type", "creditScoreType", "size", "_createTextVNode", "creditScoreText", "_hoisted_4", "_component_el_button", "icon", "onClick", "$event", "_ctx", "$router", "push", "__", "_createCommentVNode", "_hoisted_5", "link", "_hoisted_6", "_component_el_table", "data", "slice", "style", "_component_el_table_column", "prop", "label", "width", "scope", "formatDate", "row", "startTime", "formatTime", "endTime", "getStatusType", "status", "getStatusText", "viewReservation", "id", "_createBlock", "_component_el_empty", "description", "_hoisted_7", "_component_el_scrollbar", "height", "_Fragment", "_renderList", "rooms", "room", "_hoisted_9", "_hoisted_10", "name", "_hoisted_11", "_hoisted_12", "capacity", "_component_el_progress", "percentage", "Math", "round", "getProgressStatus", "_hoisted_8", "_hoisted_13", "_component_el_divider"], "sources": ["C:\\Users\\<USER>\\library_seat_system3\\frontend\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"16\">\n        <el-card class=\"welcome-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h2>欢迎使用基于国密算法的图书馆自习室座位管理系统</h2>\n            </div>\n          </template>\n          <div class=\"dashboard-content\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-statistic title=\"当前可用座位\" :value=\"availableSeats\">\n                  <template #suffix>\n                    <span class=\"statistic-suffix\">/ {{ totalSeats }}</span>\n                  </template>\n                </el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"我的预约\" :value=\"myReservations.length\">\n                  <template #suffix>\n                    <span class=\"statistic-suffix\">个</span>\n                  </template>\n                </el-statistic>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-statistic title=\"我的信誉分\" :value=\"creditScore\">\n                  <template #suffix>\n                    <el-tag :type=\"creditScoreType\" size=\"small\">{{\n                      creditScoreText\n                    }}</el-tag>\n                  </template>\n                </el-statistic>\n              </el-col>\n            </el-row>\n\n            <div class=\"quick-actions\">\n              <h3>快捷操作</h3>\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"Plus\"\n                    @click=\"$router.push('/seat/rooms')\"\n                    class=\"action-button\"\n                  >\n                    预约座位\n                  </el-button>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-button\n                    type=\"success\"\n                    icon=\"Location\"\n                    @click=\"$router.push('/seat/checkin')\"\n                    class=\"action-button\"\n                  >\n                    签到签退\n                  </el-button>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-button\n                    type=\"info\"\n                    icon=\"List\"\n                    @click=\"$router.push('/user/reservations')\"\n                    class=\"action-button\"\n                  >\n                    我的预约\n                  </el-button>\n                </el-col>\n                <el-col :span=\"6\">\n                  <el-button\n                    type=\"warning\"\n                    icon=\"View\"\n                    @click=\"$router.push('/seat/map')\"\n                    class=\"action-button\"\n                  >\n                    座位地图\n                  </el-button>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </el-card>\n\n        <!-- 最近预约 -->\n        <el-card class=\"recent-reservations-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>我的最近预约</h3>\n              <el-button\n                type=\"primary\"\n                link\n                @click=\"$router.push('/user/reservations')\"\n              >\n                查看全部\n              </el-button>\n            </div>\n          </template>\n          <div v-if=\"myReservations.length > 0\">\n            <el-table :data=\"myReservations.slice(0, 3)\" style=\"width: 100%\">\n              <el-table-column prop=\"roomName\" label=\"自习室\" width=\"180\" />\n              <el-table-column prop=\"seatNumber\" label=\"座位号\" width=\"100\" />\n              <el-table-column label=\"时间\">\n                <template #default=\"scope\">\n                  {{ formatDate(scope.row.startTime) }}\n                  {{ formatTime(scope.row.startTime) }} -\n                  {{ formatTime(scope.row.endTime) }}\n                </template>\n              </el-table-column>\n              <el-table-column label=\"状态\" width=\"100\">\n                <template #default=\"scope\">\n                  <el-tag :type=\"getStatusType(scope.row.status)\">\n                    {{ getStatusText(scope.row.status) }}\n                  </el-tag>\n                </template>\n              </el-table-column>\n              <el-table-column label=\"操作\" width=\"120\">\n                <template #default=\"scope\">\n                  <el-button\n                    link\n                    type=\"primary\"\n                    @click=\"viewReservation(scope.row.id)\"\n                  >\n                    查看\n                  </el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n          <el-empty v-else description=\"暂无预约记录\" />\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"8\">\n        <!-- 自习室状态 -->\n        <el-card class=\"room-status-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>自习室状态</h3>\n            </div>\n          </template>\n          <div class=\"room-status-list\">\n            <el-scrollbar height=\"200px\">\n              <div\n                v-for=\"room in rooms.slice(0, 6)\"\n                :key=\"room.id\"\n                class=\"room-status-item\"\n                @click=\"$router.push(`/seat/map?roomId=${room.id}`)\"\n              >\n                <div class=\"room-info\">\n                  <div class=\"room-name\">{{ room.name }}</div>\n                  <div class=\"room-capacity\">\n                    可用:\n                    <span class=\"available-seats\">{{\n                      room.availableSeats\n                    }}</span>\n                    / {{ room.capacity }}\n                  </div>\n                </div>\n                <el-progress\n                  :percentage=\"\n                    Math.round((room.availableSeats / room.capacity) * 100)\n                  \"\n                  :status=\"\n                    getProgressStatus(room.availableSeats, room.capacity)\n                  \"\n                />\n              </div>\n            </el-scrollbar>\n          </div>\n        </el-card>\n\n        <!-- 系统公告 -->\n        <el-card class=\"announcement-card\">\n          <template #header>\n            <div class=\"card-header\">\n              <h3>系统公告</h3>\n            </div>\n          </template>\n          <div class=\"announcement-list\">\n            <div class=\"announcement-item\">\n              <div class=\"announcement-title\">系统升级通知</div>\n              <div class=\"announcement-time\">2023-05-15</div>\n              <div class=\"announcement-content\">\n                系统将于本周六凌晨2:00-4:00进行升级维护，期间系统将暂停服务，请提前做好安排。\n              </div>\n            </div>\n            <el-divider />\n            <div class=\"announcement-item\">\n              <div class=\"announcement-title\">图书馆开放时间调整</div>\n              <div class=\"announcement-time\">2023-05-10</div>\n              <div class=\"announcement-content\">\n                自5月15日起，图书馆开放时间调整为8:00-22:00，请知悉。\n              </div>\n            </div>\n          </div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted } from \"vue\";\nimport { useRouter } from \"vue-router\";\nimport { useStore } from \"vuex\";\n// 导入API\nimport seatApi from \"@/api/seat\";\nimport userApi from \"@/api/user\";\n\nexport default {\n  name: \"DashboardView\",\n  setup() {\n    const router = useRouter();\n    const store = useStore();\n\n    // 统计数据\n    const availableSeats = ref(0);\n    const totalSeats = ref(0);\n    const creditScore = ref(0);\n    const loading = ref(true);\n\n    // 计算信誉分状态\n    const creditScoreType = computed(() => {\n      if (creditScore.value >= 90) return \"success\";\n      if (creditScore.value >= 70) return \"warning\";\n      return \"danger\";\n    });\n\n    const creditScoreText = computed(() => {\n      if (creditScore.value >= 90) return \"优秀\";\n      if (creditScore.value >= 70) return \"良好\";\n      if (creditScore.value >= 50) return \"一般\";\n      return \"较差\";\n    });\n\n    // 计算可用座位总数\n    const calculateAvailableSeats = () => {\n      let available = 0;\n      let total = 0;\n\n      rooms.forEach((room) => {\n        available += room.availableSeats;\n        total += room.capacity;\n      });\n\n      availableSeats.value = available;\n      totalSeats.value = total;\n    };\n\n    // 获取预约状态类型\n    const getStatusType = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"warning\";\n        case \"checked_in\":\n          return \"success\";\n        case \"completed\":\n          return \"info\";\n        case \"cancelled\":\n          return \"danger\";\n        case \"timeout\":\n          return \"danger\";\n        default:\n          return \"info\";\n      }\n    };\n\n    // 获取预约状态文本\n    const getStatusText = (status) => {\n      switch (status) {\n        case \"pending\":\n          return \"待签到\";\n        case \"checked_in\":\n          return \"已签到\";\n        case \"completed\":\n          return \"已完成\";\n        case \"cancelled\":\n          return \"已取消\";\n        case \"timeout\":\n          return \"已超时\";\n        default:\n          return \"未知\";\n      }\n    };\n\n    // 获取进度条状态\n    const getProgressStatus = (available, total) => {\n      const percentage = (available / total) * 100;\n      if (percentage < 20) return \"exception\";\n      if (percentage < 50) return \"warning\";\n      return \"success\";\n    };\n\n    // 格式化日期\n    const formatDate = (dateString) => {\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(\n        2,\n        \"0\"\n      )}-${String(date.getDate()).padStart(2, \"0\")}`;\n    };\n\n    // 格式化时间\n    const formatTime = (dateString) => {\n      const date = new Date(dateString);\n      return `${String(date.getHours()).padStart(2, \"0\")}:${String(\n        date.getMinutes()\n      ).padStart(2, \"0\")}`;\n    };\n\n    // 查看预约详情\n    const viewReservation = (id) => {\n      router.push(`/seat/reservation/${id}`);\n    };\n\n    onMounted(() => {\n      calculateAvailableSeats();\n    });\n\n    return {\n      rooms,\n      myReservations,\n      availableSeats,\n      totalSeats,\n      creditScore,\n      creditScoreType,\n      creditScoreText,\n      getStatusType,\n      getStatusText,\n      getProgressStatus,\n      formatDate,\n      formatTime,\n      viewReservation,\n    };\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-container {\n  padding: 20px;\n}\n\n.welcome-card,\n.recent-reservations-card,\n.room-status-card,\n.announcement-card {\n  margin-bottom: 20px;\n  transition: all 0.3s;\n\n  &:hover {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  h2,\n  h3 {\n    margin: 0;\n  }\n}\n\n.dashboard-content {\n  padding: 20px 0;\n}\n\n.statistic-suffix {\n  font-size: 14px;\n  color: #909399;\n  margin-left: 5px;\n}\n\n.quick-actions {\n  margin-top: 30px;\n\n  h3 {\n    margin-bottom: 20px;\n    font-size: 18px;\n    color: #303133;\n  }\n\n  .action-button {\n    width: 100%;\n    height: 50px;\n    font-size: 16px;\n    transition: all 0.3s;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n  }\n}\n\n.room-status-list {\n  .room-status-item {\n    padding: 10px;\n    margin-bottom: 15px;\n    border-radius: 4px;\n    background-color: #f5f7fa;\n    cursor: pointer;\n    transition: all 0.3s;\n\n    &:hover {\n      background-color: #ecf5ff;\n    }\n\n    .room-info {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 8px;\n\n      .room-name {\n        font-weight: bold;\n      }\n\n      .room-capacity {\n        font-size: 14px;\n        color: #606266;\n\n        .available-seats {\n          color: #67c23a;\n          font-weight: bold;\n        }\n      }\n    }\n  }\n}\n\n.announcement-list {\n  .announcement-item {\n    margin-bottom: 15px;\n\n    .announcement-title {\n      font-weight: bold;\n      font-size: 16px;\n      margin-bottom: 5px;\n    }\n\n    .announcement-time {\n      font-size: 12px;\n      color: #909399;\n      margin-bottom: 8px;\n    }\n\n    .announcement-content {\n      font-size: 14px;\n      color: #606266;\n      line-height: 1.5;\n    }\n  }\n}\n\n.recent-reservations-card {\n  margin-top: 20px;\n}\n</style>\n"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EASnBA,KAAK,EAAC;AAAmB;;EAKdA,KAAK,EAAC;AAAkB;;EAsBjCA,KAAK,EAAC;AAAe;;EAmDrBA,KAAK,EAAC;AAAa;;EAxFpCC,GAAA;AAAA;;EA8IeD,KAAK,EAAC;AAAkB;mBA9IvC;;EAsJqBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAe;;EAElBA,KAAK,EAAC;AAAiB;;EA0BlCA,KAAK,EAAC;AAAmB;;;;;;;;;;;;;;uBAnLtCE,mBAAA,CAuMM,OAvMNC,UAuMM,GAtMJC,YAAA,CAqMSC,iBAAA;IArMAC,MAAM,EAAE;EAAE;IAFvBC,OAAA,EAAAC,QAAA,CAGM,MAiIS,CAjITJ,YAAA,CAiISK,iBAAA;MAjIAC,IAAI,EAAE;IAAE;MAHvBH,OAAA,EAAAC,QAAA,CAIQ,MA+EU,CA/EVJ,YAAA,CA+EUO,kBAAA;QA/EDX,KAAK,EAAC;MAAc;QAChBY,MAAM,EAAAJ,QAAA,CACf,MAEMK,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;UAFDd,KAAK,EAAC;QAAa,IACtBc,mBAAA,CAAgC,YAA5B,yBAAuB,E;QAPzCP,OAAA,EAAAC,QAAA,CAUU,MAwEM,CAxENM,mBAAA,CAwEM,OAxENC,UAwEM,GAvEJX,YAAA,CAwBSC,iBAAA;UAxBAC,MAAM,EAAE;QAAE;UAX/BC,OAAA,EAAAC,QAAA,CAYc,MAMS,CANTJ,YAAA,CAMSK,iBAAA;YANAC,IAAI,EAAE;UAAC;YAZ9BH,OAAA,EAAAC,QAAA,CAagB,MAIe,CAJfJ,YAAA,CAIeY,uBAAA;cAJDC,KAAK,EAAC,QAAQ;cAAEC,KAAK,EAAEC,MAAA,CAAAC;;cACxBC,MAAM,EAAAb,QAAA,CACf,MAAwD,CAAxDM,mBAAA,CAAwD,QAAxDQ,UAAwD,EAAzB,IAAE,GAAAC,gBAAA,CAAGJ,MAAA,CAAAK,UAAU,iB;cAflEC,CAAA;;YAAAA,CAAA;cAmBcrB,YAAA,CAMSK,iBAAA;YANAC,IAAI,EAAE;UAAC;YAnB9BH,OAAA,EAAAC,QAAA,CAoBgB,MAIe,CAJfJ,YAAA,CAIeY,uBAAA;cAJDC,KAAK,EAAC,MAAM;cAAEC,KAAK,EAAEC,MAAA,CAAAO,cAAc,CAACC;;cACrCN,MAAM,EAAAb,QAAA,CACf,MAAuCK,MAAA,QAAAA,MAAA,OAAvCC,mBAAA,CAAuC;gBAAjCd,KAAK,EAAC;cAAkB,GAAC,GAAC,oB;cAtBpDyB,CAAA;;YAAAA,CAAA;cA0BcrB,YAAA,CAQSK,iBAAA;YARAC,IAAI,EAAE;UAAC;YA1B9BH,OAAA,EAAAC,QAAA,CA2BgB,MAMe,CANfJ,YAAA,CAMeY,uBAAA;cANDC,KAAK,EAAC,OAAO;cAAEC,KAAK,EAAEC,MAAA,CAAAS;;cACvBP,MAAM,EAAAb,QAAA,CACf,MAEW,CAFXJ,YAAA,CAEWyB,iBAAA;gBAFFC,IAAI,EAAEX,MAAA,CAAAY,eAAe;gBAAEC,IAAI,EAAC;;gBA7BzDzB,OAAA,EAAAC,QAAA,CA6BiE,MAE3C,CA/BtByB,gBAAA,CAAAV,gBAAA,CA8BsBJ,MAAA,CAAAe,eAAe,iB;gBA9BrCT,CAAA;;cAAAA,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;YAqCYX,mBAAA,CA4CM,OA5CNqB,UA4CM,G,4BA3CJrB,mBAAA,CAAa,YAAT,MAAI,sBACRV,YAAA,CAyCSC,iBAAA;UAzCAC,MAAM,EAAE;QAAE;UAvCjCC,OAAA,EAAAC,QAAA,CAwCgB,MASS,CATTJ,YAAA,CASSK,iBAAA;YATAC,IAAI,EAAE;UAAC;YAxChCH,OAAA,EAAAC,QAAA,CAyCkB,MAOY,CAPZJ,YAAA,CAOYgC,oBAAA;cANVN,IAAI,EAAC,SAAS;cACdO,IAAI,EAAC,MAAM;cACVC,OAAK,EAAAzB,MAAA,QAAAA,MAAA,MAAA0B,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;cACpB1C,KAAK,EAAC;;cA7C1BO,OAAA,EAAAC,QAAA,CA8CmB,MAEDK,MAAA,QAAAA,MAAA,OAhDlBoB,gBAAA,CA8CmB,QAED,E;cAhDlBR,CAAA;cAAAkB,EAAA;;YAAAlB,CAAA;cAkDgBrB,YAAA,CASSK,iBAAA;YATAC,IAAI,EAAE;UAAC;YAlDhCH,OAAA,EAAAC,QAAA,CAmDkB,MAOY,CAPZJ,YAAA,CAOYgC,oBAAA;cANVN,IAAI,EAAC,SAAS;cACdO,IAAI,EAAC,UAAU;cACdC,OAAK,EAAAzB,MAAA,QAAAA,MAAA,MAAA0B,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;cACpB1C,KAAK,EAAC;;cAvD1BO,OAAA,EAAAC,QAAA,CAwDmB,MAEDK,MAAA,QAAAA,MAAA,OA1DlBoB,gBAAA,CAwDmB,QAED,E;cA1DlBR,CAAA;cAAAkB,EAAA;;YAAAlB,CAAA;cA4DgBrB,YAAA,CASSK,iBAAA;YATAC,IAAI,EAAE;UAAC;YA5DhCH,OAAA,EAAAC,QAAA,CA6DkB,MAOY,CAPZJ,YAAA,CAOYgC,oBAAA;cANVN,IAAI,EAAC,MAAM;cACXO,IAAI,EAAC,MAAM;cACVC,OAAK,EAAAzB,MAAA,QAAAA,MAAA,MAAA0B,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;cACpB1C,KAAK,EAAC;;cAjE1BO,OAAA,EAAAC,QAAA,CAkEmB,MAEDK,MAAA,QAAAA,MAAA,OApElBoB,gBAAA,CAkEmB,QAED,E;cApElBR,CAAA;cAAAkB,EAAA;;YAAAlB,CAAA;cAsEgBrB,YAAA,CASSK,iBAAA;YATAC,IAAI,EAAE;UAAC;YAtEhCH,OAAA,EAAAC,QAAA,CAuEkB,MAOY,CAPZJ,YAAA,CAOYgC,oBAAA;cANVN,IAAI,EAAC,SAAS;cACdO,IAAI,EAAC,MAAM;cACVC,OAAK,EAAAzB,MAAA,QAAAA,MAAA,MAAA0B,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;cACpB1C,KAAK,EAAC;;cA3E1BO,OAAA,EAAAC,QAAA,CA4EmB,MAEDK,MAAA,SAAAA,MAAA,QA9ElBoB,gBAAA,CA4EmB,QAED,E;cA9ElBR,CAAA;cAAAkB,EAAA;;YAAAlB,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAqFQmB,mBAAA,UAAa,EACbxC,YAAA,CA6CUO,kBAAA;QA7CDX,KAAK,EAAC;MAA0B;QAC5BY,MAAM,EAAAJ,QAAA,CACf,MASM,CATNM,mBAAA,CASM,OATN+B,UASM,G,4BARJ/B,mBAAA,CAAe,YAAX,QAAM,sBACVV,YAAA,CAMYgC,oBAAA;UALVN,IAAI,EAAC,SAAS;UACdgB,IAAI,EAAJ,EAAI;UACHR,OAAK,EAAAzB,MAAA,QAAAA,MAAA,MAAA0B,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;UA7FpCnC,OAAA,EAAAC,QAAA,CA8Fe,MAEDK,MAAA,SAAAA,MAAA,QAhGdoB,gBAAA,CA8Fe,QAED,E;UAhGdR,CAAA;UAAAkB,EAAA;;QAAApC,OAAA,EAAAC,QAAA,CAmGU,MA8BM,CA9BKW,MAAA,CAAAO,cAAc,CAACC,MAAM,Q,cAAhCzB,mBAAA,CA8BM,OAjIhB6C,UAAA,GAoGY3C,YAAA,CA4BW4C,mBAAA;UA5BAC,IAAI,EAAE9B,MAAA,CAAAO,cAAc,CAACwB,KAAK;UAAQC,KAAmB,EAAnB;YAAA;UAAA;;UApGzD5C,OAAA,EAAAC,QAAA,CAqGc,MAA2D,CAA3DJ,YAAA,CAA2DgD,0BAAA;YAA1CC,IAAI,EAAC,UAAU;YAACC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;cACnDnD,YAAA,CAA6DgD,0BAAA;YAA5CC,IAAI,EAAC,YAAY;YAACC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;cACrDnD,YAAA,CAMkBgD,0BAAA;YANDE,KAAK,EAAC;UAAI;YACd/C,OAAO,EAAAC,QAAA,CACqBgD,KADd,KAxGzCvB,gBAAA,CAAAV,gBAAA,CAyGqBJ,MAAA,CAAAsC,UAAU,CAACD,KAAK,CAACE,GAAG,CAACC,SAAS,KAAI,GACrC,GAAApC,gBAAA,CAAGJ,MAAA,CAAAyC,UAAU,CAACJ,KAAK,CAACE,GAAG,CAACC,SAAS,KAAI,KACrC,GAAApC,gBAAA,CAAGJ,MAAA,CAAAyC,UAAU,CAACJ,KAAK,CAACE,GAAG,CAACG,OAAO,kB;YA3GjDpC,CAAA;cA8GcrB,YAAA,CAMkBgD,0BAAA;YANDE,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;;YACrBhD,OAAO,EAAAC,QAAA,CAGPgD,KAHc,KACvBpD,YAAA,CAESyB,iBAAA;cAFAC,IAAI,EAAEX,MAAA,CAAA2C,aAAa,CAACN,KAAK,CAACE,GAAG,CAACK,MAAM;;cAhH/DxD,OAAA,EAAAC,QAAA,CAiHoB,MAAqC,CAjHzDyB,gBAAA,CAAAV,gBAAA,CAiHuBJ,MAAA,CAAA6C,aAAa,CAACR,KAAK,CAACE,GAAG,CAACK,MAAM,kB;cAjHrDtC,CAAA;;YAAAA,CAAA;cAqHcrB,YAAA,CAUkBgD,0BAAA;YAVDE,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;;YACrBhD,OAAO,EAAAC,QAAA,CAOJgD,KAPW,KACvBpD,YAAA,CAMYgC,oBAAA;cALVU,IAAI,EAAJ,EAAI;cACJhB,IAAI,EAAC,SAAS;cACbQ,OAAK,EAAAC,MAAA,IAAEpB,MAAA,CAAA8C,eAAe,CAACT,KAAK,CAACE,GAAG,CAACQ,EAAE;;cA1HxD3D,OAAA,EAAAC,QAAA,CA2HmB,MAEDK,MAAA,SAAAA,MAAA,QA7HlBoB,gBAAA,CA2HmB,MAED,E;cA7HlBR,CAAA;cAAAkB,EAAA;;YAAAlB,CAAA;;UAAAA,CAAA;wDAkIU0C,YAAA,CAAwCC,mBAAA;UAlIlDnE,GAAA;UAkI2BoE,WAAW,EAAC;;QAlIvC5C,CAAA;;MAAAA,CAAA;QAsIMrB,YAAA,CAgESK,iBAAA;MAhEAC,IAAI,EAAE;IAAC;MAtItBH,OAAA,EAAAC,QAAA,CAuIQ,MAAc,CAAdoC,mBAAA,WAAc,EACdxC,YAAA,CAmCUO,kBAAA;QAnCDX,KAAK,EAAC;MAAkB;QACpBY,MAAM,EAAAJ,QAAA,CACf,MAEMK,MAAA,SAAAA,MAAA,QAFNC,mBAAA,CAEM;UAFDd,KAAK,EAAC;QAAa,IACtBc,mBAAA,CAAc,YAAV,OAAK,E;QA3IvBP,OAAA,EAAAC,QAAA,CA8IU,MA4BM,CA5BNM,mBAAA,CA4BM,OA5BNwD,UA4BM,GA3BJlE,YAAA,CA0BemE,uBAAA;UA1BDC,MAAM,EAAC;QAAO;UA/IxCjE,OAAA,EAAAC,QAAA,CAiJgB,MAAiC,E,kBADnCN,mBAAA,CAwBMuE,SAAA,QAxKpBC,WAAA,CAiJ+BvD,MAAA,CAAAwD,KAAK,CAACzB,KAAK,QAAnB0B,IAAI;iCADb1E,mBAAA,CAwBM;cAtBHD,GAAG,EAAE2E,IAAI,CAACV,EAAE;cACblE,KAAK,EAAC,kBAAkB;cACvBsC,OAAK,EAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI,qBAAqBkC,IAAI,CAACV,EAAE;gBAEhDpD,mBAAA,CASM,OATN+D,UASM,GARJ/D,mBAAA,CAA4C,OAA5CgE,WAA4C,EAAAvD,gBAAA,CAAlBqD,IAAI,CAACG,IAAI,kBACnCjE,mBAAA,CAMM,OANNkE,WAMM,G,4BA9JxB/C,gBAAA,CAwJ6C,OAEzB,IAAAnB,mBAAA,CAES,QAFTmE,WAES,EAAA1D,gBAAA,CADPqD,IAAI,CAACxD,cAAc,kBA3JzCa,gBAAA,CA4J6B,KACP,GAAAV,gBAAA,CAAGqD,IAAI,CAACM,QAAQ,iB,KAGtB9E,YAAA,CAOE+E,sBAAA;cANCC,UAAU,EAAuBC,IAAI,CAACC,KAAK,CAAEV,IAAI,CAACxD,cAAc,GAAGwD,IAAI,CAACM,QAAQ;cAGhFnB,MAAM,EAAuB5C,MAAA,CAAAoE,iBAAiB,CAACX,IAAI,CAACxD,cAAc,EAAEwD,IAAI,CAACM,QAAQ;+EApKpGM,UAAA;;UAAA/D,CAAA;;QAAAA,CAAA;UA6KQmB,mBAAA,UAAa,EACbxC,YAAA,CAuBUO,kBAAA;QAvBDX,KAAK,EAAC;MAAmB;QACrBY,MAAM,EAAAJ,QAAA,CACf,MAEMK,MAAA,SAAAA,MAAA,QAFNC,mBAAA,CAEM;UAFDd,KAAK,EAAC;QAAa,IACtBc,mBAAA,CAAa,YAAT,MAAI,E;QAjLtBP,OAAA,EAAAC,QAAA,CAoLU,MAgBM,CAhBNM,mBAAA,CAgBM,OAhBN2E,WAgBM,G,4BAfJ3E,mBAAA,CAMM;UANDd,KAAK,EAAC;QAAmB,IAC5Bc,mBAAA,CAA4C;UAAvCd,KAAK,EAAC;QAAoB,GAAC,QAAM,GACtCc,mBAAA,CAA+C;UAA1Cd,KAAK,EAAC;QAAmB,GAAC,YAAU,GACzCc,mBAAA,CAEM;UAFDd,KAAK,EAAC;QAAsB,GAAC,+CAElC,E,sBAEFI,YAAA,CAAcsF,qBAAA,G,4BACd5E,mBAAA,CAMM;UANDd,KAAK,EAAC;QAAmB,IAC5Bc,mBAAA,CAA+C;UAA1Cd,KAAK,EAAC;QAAoB,GAAC,WAAS,GACzCc,mBAAA,CAA+C;UAA1Cd,KAAK,EAAC;QAAmB,GAAC,YAAU,GACzCc,mBAAA,CAEM;UAFDd,KAAK,EAAC;QAAsB,GAAC,qCAElC,E;QAlMdyB,CAAA;;MAAAA,CAAA;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}