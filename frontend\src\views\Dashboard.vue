<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card class="welcome-card">
          <template #header>
            <div class="card-header">
              <h2>欢迎使用基于国密算法的图书馆自习室座位管理系统</h2>
            </div>
          </template>
          <div class="dashboard-content">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-statistic title="当前可用座位" :value="availableSeats">
                  <template #suffix>
                    <span class="statistic-suffix">/ {{ totalSeats }}</span>
                  </template>
                </el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic title="我的预约" :value="recentReservations.length">
                  <template #suffix>
                    <span class="statistic-suffix">个</span>
                  </template>
                </el-statistic>
              </el-col>
              <el-col :span="8">
                <el-statistic title="我的信誉分" :value="creditScore">
                  <template #suffix>
                    <el-tag :type="creditScoreType" size="small">{{
                      creditScoreText
                    }}</el-tag>
                  </template>
                </el-statistic>
              </el-col>
            </el-row>

            <div class="quick-actions">
              <h3>快捷操作</h3>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-button
                    type="primary"
                    icon="Plus"
                    @click="$router.push('/seat/rooms')"
                    class="action-button"
                  >
                    预约座位
                  </el-button>
                </el-col>
                <el-col :span="6">
                  <el-button
                    type="success"
                    icon="Location"
                    @click="$router.push('/seat/checkin')"
                    class="action-button"
                  >
                    签到签退
                  </el-button>
                </el-col>
                <el-col :span="6">
                  <el-button
                    type="info"
                    icon="List"
                    @click="$router.push('/user/reservations')"
                    class="action-button"
                  >
                    我的预约
                  </el-button>
                </el-col>
                <el-col :span="6">
                  <el-button
                    type="warning"
                    icon="View"
                    @click="$router.push('/seat/map')"
                    class="action-button"
                  >
                    座位地图
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-card>

        <!-- 最近预约 -->
        <el-card class="recent-reservations-card">
          <template #header>
            <div class="card-header">
              <h3>我的最近预约</h3>
              <el-button
                type="primary"
                link
                @click="$router.push('/user/reservations')"
              >
                查看全部
              </el-button>
            </div>
          </template>
          <div v-if="recentReservations.length > 0">
            <el-table :data="recentReservations" style="width: 100%">
              <el-table-column prop="roomName" label="自习室" width="180" />
              <el-table-column prop="seatNumber" label="座位号" width="100" />
              <el-table-column label="时间">
                <template #default="scope">
                  {{ formatDate(scope.row.startTime) }}
                  {{ formatTime(scope.row.startTime) }} -
                  {{ formatTime(scope.row.endTime) }}
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    @click="viewReservation(scope.row.id)"
                  >
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-empty v-else description="暂无预约记录" />
        </el-card>
      </el-col>

      <el-col :span="8">
        <!-- 自习室状态 -->
        <el-card class="room-status-card">
          <template #header>
            <div class="card-header">
              <h3>自习室状态</h3>
            </div>
          </template>
          <div class="room-status-list">
            <el-scrollbar height="200px">
              <div
                v-for="room in rooms.slice(0, 6)"
                :key="room.id"
                class="room-status-item"
                @click="$router.push(`/seat/map?roomId=${room.id}`)"
              >
                <div class="room-info">
                  <div class="room-name">{{ room.name }}</div>
                  <div class="room-capacity">
                    可用:
                    <span class="available-seats">{{
                      room.availableSeats
                    }}</span>
                    / {{ room.capacity }}
                  </div>
                </div>
                <el-progress
                  :percentage="
                    Math.round((room.availableSeats / room.capacity) * 100)
                  "
                  :status="
                    getProgressStatus(room.availableSeats, room.capacity)
                  "
                />
              </div>
            </el-scrollbar>
          </div>
        </el-card>

        <!-- 系统公告 -->
        <el-card class="announcement-card">
          <template #header>
            <div class="card-header">
              <h3>系统公告</h3>
            </div>
          </template>
          <div class="announcement-list">
            <div class="announcement-item">
              <div class="announcement-title">系统升级通知</div>
              <div class="announcement-time">2023-05-15</div>
              <div class="announcement-content">
                系统将于本周六凌晨2:00-4:00进行升级维护，期间系统将暂停服务，请提前做好安排。
              </div>
            </div>
            <el-divider />
            <div class="announcement-item">
              <div class="announcement-title">图书馆开放时间调整</div>
              <div class="announcement-time">2023-05-10</div>
              <div class="announcement-content">
                自5月15日起，图书馆开放时间调整为8:00-22:00，请知悉。
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
// 导入API
import seatApi from "@/api/seat";
import userApi from "@/api/user";

export default {
  name: "DashboardView",
  setup() {
    const router = useRouter();
    const store = useStore();

    // 统计数据
    const availableSeats = ref(0);
    const totalSeats = ref(0);
    const creditScore = ref(0);
    const loading = ref(true);

    // 计算信誉分状态
    const creditScoreType = computed(() => {
      if (creditScore.value >= 90) return "success";
      if (creditScore.value >= 70) return "warning";
      return "danger";
    });

    const creditScoreText = computed(() => {
      if (creditScore.value >= 90) return "优秀";
      if (creditScore.value >= 70) return "良好";
      if (creditScore.value >= 50) return "一般";
      return "较差";
    });

    // 最近预约
    const recentReservations = ref([]);

    // 加载统计数据
    const loadStatistics = async () => {
      try {
        loading.value = true;

        // 获取自习室列表计算座位统计
        const roomsResponse = await seatApi.getRooms();
        const rooms = roomsResponse.data.results || roomsResponse.data;

        totalSeats.value = rooms.reduce((sum, room) => sum + room.capacity, 0);
        availableSeats.value = rooms.reduce((sum, room) => sum + (room.available_seats || 0), 0);

        // 获取用户信息
        const userResponse = await userApi.getUserInfo();
        creditScore.value = userResponse.data.credit_score || 0;

        // 获取最近预约
        const reservationsResponse = await seatApi.getMyReservations();
        const allReservations = reservationsResponse.data.results || reservationsResponse.data;
        recentReservations.value = allReservations.slice(0, 3);

      } catch (error) {
        console.error("加载统计数据失败:", error);
      } finally {
        loading.value = false;
      }
    };

    // 跳转到自习室列表
    const goToRooms = () => {
      router.push("/seat/rooms");
    };

    // 跳转到我的预约
    const goToMyReservations = () => {
      router.push("/user/reservations");
    };

    // 跳转到信誉分记录
    const goToCreditRecords = () => {
      router.push("/user/credit-records");
    };

    // 获取预约状态类型
    const getStatusType = (status) => {
      switch (status) {
        case "pending":
          return "warning";
        case "checked_in":
          return "success";
        case "completed":
          return "info";
        case "cancelled":
          return "danger";
        case "timeout":
          return "danger";
        default:
          return "info";
      }
    };

    // 获取预约状态文本
    const getStatusText = (status) => {
      switch (status) {
        case "pending":
          return "待签到";
        case "checked_in":
          return "已签到";
        case "completed":
          return "已完成";
        case "cancelled":
          return "已取消";
        case "timeout":
          return "已超时";
        default:
          return "未知";
      }
    };

    // 获取进度条状态
    const getProgressStatus = (available, total) => {
      const percentage = (available / total) * 100;
      if (percentage < 20) return "exception";
      if (percentage < 50) return "warning";
      return "success";
    };

    // 格式化日期
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}`;
    };

    // 格式化时间
    const formatTime = (dateString) => {
      const date = new Date(dateString);
      return `${String(date.getHours()).padStart(2, "0")}:${String(
        date.getMinutes()
      ).padStart(2, "0")}`;
    };

    // 查看预约详情
    const viewReservation = (id) => {
      router.push(`/seat/reservation/${id}`);
    };

    onMounted(() => {
      loadStatistics();
    });

    return {
      recentReservations,
      availableSeats,
      totalSeats,
      creditScore,
      creditScoreType,
      creditScoreText,
      loading,
      getStatusType,
      getStatusText,
      getProgressStatus,
      formatDate,
      formatTime,
      viewReservation,
      goToRooms,
      goToMyReservations,
      goToCreditRecords,
    };
  },
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-card,
.recent-reservations-card,
.room-status-card,
.announcement-card {
  margin-bottom: 20px;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2,
  h3 {
    margin: 0;
  }
}

.dashboard-content {
  padding: 20px 0;
}

.statistic-suffix {
  font-size: 14px;
  color: #909399;
  margin-left: 5px;
}

.quick-actions {
  margin-top: 30px;

  h3 {
    margin-bottom: 20px;
    font-size: 18px;
    color: #303133;
  }

  .action-button {
    width: 100%;
    height: 50px;
    font-size: 16px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
    }
  }
}

.room-status-list {
  .room-status-item {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    background-color: #f5f7fa;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background-color: #ecf5ff;
    }

    .room-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .room-name {
        font-weight: bold;
      }

      .room-capacity {
        font-size: 14px;
        color: #606266;

        .available-seats {
          color: #67c23a;
          font-weight: bold;
        }
      }
    }
  }
}

.announcement-list {
  .announcement-item {
    margin-bottom: 15px;

    .announcement-title {
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 5px;
    }

    .announcement-time {
      font-size: 12px;
      color: #909399;
      margin-bottom: 8px;
    }

    .announcement-content {
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
    }
  }
}

.recent-reservations-card {
  margin-top: 20px;
}
</style>
